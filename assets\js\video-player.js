// Video Player Page functionality
class VideoPlayer {
    constructor(app) {
        this.app = app;
        this.currentVideo = null;
        this.videoId = this.getVideoIdFromURL();
        
        this.init();
    }
    
    async init() {
        // Wait for app to be ready
        if (!this.app.videos.length) {
            setTimeout(() => this.init(), 100);
            return;
        }
        
        if (this.videoId) {
            this.loadVideo(this.videoId);
        } else {
            this.showError('Video not found');
        }
        
        this.setupEventListeners();
    }
    
    getVideoIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id');
    }
    
    loadVideo(videoId) {
        console.log('VideoPlayer: Looking for video with ID:', videoId);
        
        this.currentVideo = this.app.getVideoById(videoId);
        
        if (!this.currentVideo) {
            console.error('VideoPlayer: Video not found for ID:', videoId);
            console.log('VideoPlayer: Available video IDs:', this.app.videos.map(v => v.id));
            
            this.showError(`Video not found. Requested ID: ${videoId}`);
            return;
        }
        
        this.displayVideo();
        this.loadComments();
        this.loadRelatedVideos();
        this.loadOtherVideos();
        this.updatePageTitle();
    }
    
    displayVideo() {
        const video = this.currentVideo;

        // Handle photo display differently
        if (video.type === 'photo') {
            this.displayPhoto(video);
            return;
        }

        // Update video player with multiple format support
        const videoPlayer = document.getElementById('videoPlayer');
        if (videoPlayer) {
            // Clear existing sources
            videoPlayer.innerHTML = '';

            // Add multiple source formats
            const sources = this.getVideoSources(video);
            sources.forEach(source => {
                const sourceElement = document.createElement('source');
                sourceElement.src = source.url;
                sourceElement.type = source.type;
                videoPlayer.appendChild(sourceElement);
            });

            // Add fallback text
            const fallbackText = document.createElement('p');
            fallbackText.textContent = 'Your browser does not support the video tag.';
            videoPlayer.appendChild(fallbackText);

            // Set poster image with proper path resolution
            const posterSrc = this.app.getThumbnailSrc(video);
            videoPlayer.poster = posterSrc;
            
            // Preload metadata for better UX
            videoPlayer.preload = 'metadata';

            // Add error handling for unsupported formats
            videoPlayer.addEventListener('error', () => {
                this.handleVideoError(video);
            });
        }
        
        // Update video info
        const videoTitle = document.getElementById('videoTitle');
        if (videoTitle) videoTitle.textContent = video.title;
        
        const videoViews = document.getElementById('videoViews');
        if (videoViews) videoViews.textContent = `${this.app.formatNumber(video.views)} views`;
        
        const uploadDate = document.getElementById('uploadDate');
        if (uploadDate) uploadDate.textContent = this.app.formatDate(video.uploadDate);
        
        const videoDuration = document.getElementById('videoDuration');
        if (videoDuration) videoDuration.textContent = video.duration;
        
        const videoDescription = document.getElementById('videoDescription');
        if (videoDescription) videoDescription.textContent = video.description;
        
        // Update like count
        const likeCount = document.getElementById('likeCount');
        if (likeCount) likeCount.textContent = this.app.formatNumber(video.likes);
        
        // Update tags
        this.displayTags();
        
        // Update uploader info
        this.displayUploaderInfo();
        
        // Update action buttons state
        this.updateActionButtons();
    }

    getVideoSources(video) {
        const sources = [];

        // Primary video URL (check both videoUrl and url properties)
        const primaryUrl = video.videoUrl || video.url;
        if (primaryUrl) {
            const extension = this.getFileExtension(primaryUrl);
            sources.push({
                url: this.resolvePath(primaryUrl),
                type: this.getMimeType(extension)
            });
        }

        // Additional format URLs if available
        if (video.webmUrl) {
            sources.push({
                url: video.webmUrl,
                type: 'video/webm'
            });
        }

        if (video.mp4Url) {
            sources.push({
                url: video.mp4Url,
                type: 'video/mp4'
            });
        }

        if (video.oggUrl) {
            sources.push({
                url: video.oggUrl,
                type: 'video/ogg'
            });
        }

        if (video.aviUrl) {
            sources.push({
                url: video.aviUrl,
                type: 'video/avi'
            });
        }

        if (video.movUrl) {
            sources.push({
                url: video.movUrl,
                type: 'video/quicktime'
            });
        }

        if (video.flvUrl) {
            sources.push({
                url: video.flvUrl,
                type: 'video/x-flv'
            });
        }

        if (video.mkvUrl) {
            sources.push({
                url: video.mkvUrl,
                type: 'video/x-matroska'
            });
        }

        return sources;
    }

    getFileExtension(url) {
        return url.split('.').pop().toLowerCase();
    }

    getMimeType(extension) {
        const mimeTypes = {
            'mp4': 'video/mp4',
            'webm': 'video/webm',
            'ogg': 'video/ogg',
            'ogv': 'video/ogg',
            'avi': 'video/avi',
            'mov': 'video/quicktime',
            'flv': 'video/x-flv',
            'mkv': 'video/x-matroska',
            'ts': 'video/mp2t',
            'm4v': 'video/mp4',
            '3gp': 'video/3gpp',
            'wmv': 'video/x-ms-wmv'
        };

        return mimeTypes[extension] || 'video/mp4';
    }

    resolvePath(path) {
        if (!path) return path;

        // If already absolute or starts with http, return as is
        if (path.startsWith('http')) {
            return path;
        }

        // Normalize path - remove leading slash if present
        let normalizedPath = path.startsWith('/') ? path.substring(1) : path;

        // Replace backslashes with forward slashes for cross-platform compatibility
        normalizedPath = normalizedPath.replace(/\\/g, '/');

        // Get the current page location
        const currentPath = window.location.pathname;
        const currentDir = currentPath.substring(0, currentPath.lastIndexOf('/'));

        // Determine if we're in a subdirectory
        const isInSubdirectory = currentPath.includes('/pages/') ||
                                currentDir.split('/').filter(segment => segment.length > 0).length > 0;

        // For production servers, always use relative paths from root
        if (isInSubdirectory) {
            return `../${normalizedPath}`;
        } else {
            return normalizedPath;
        }
    }

    displayPhoto(photo) {
        // Hide video player and show photo viewer
        const videoPlayer = document.getElementById('videoPlayer');
        if (videoPlayer) {
            videoPlayer.style.display = 'none';
        }

        // Create or update photo viewer
        let photoViewer = document.getElementById('photoViewer');
        if (!photoViewer) {
            photoViewer = document.createElement('div');
            photoViewer.id = 'photoViewer';
            photoViewer.className = 'photo-viewer';
            videoPlayer.parentNode.insertBefore(photoViewer, videoPlayer);
        }

        // Get the photo URL (could be in videoUrl, url, or thumbnail property)
        const photoUrl = photo.videoUrl || photo.url || photo.thumbnail;
        const resolvedPhotoUrl = this.resolvePath(photoUrl);

        photoViewer.innerHTML = `
            <img src="${resolvedPhotoUrl}" alt="${photo.title}" class="photo-display">
            <div class="photo-controls">
                <button class="photo-btn" onclick="this.downloadPhoto('${resolvedPhotoUrl}', '${photo.title}')">
                    <i class="fas fa-download"></i> Download
                </button>
                <button class="photo-btn" onclick="this.openPhotoFullscreen('${resolvedPhotoUrl}')">
                    <i class="fas fa-expand"></i> Fullscreen
                </button>
            </div>
        `;

        photoViewer.style.display = 'block';
    }

    handleVideoError(video) {
        const videoPlayer = document.getElementById('videoPlayer');
        if (videoPlayer) {
            videoPlayer.innerHTML = `
                <div class="video-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Video Format Not Supported</h3>
                    <p>This video format is not supported by your browser.</p>
                    <div class="format-info">
                        <p><strong>Available formats:</strong></p>
                        <ul>
                            ${this.getAvailableFormats(video).map(format => `<li>${format}</li>`).join('')}
                        </ul>
                    </div>
                    <button class="download-btn" onclick="window.open('${video.videoUrl || video.url}', '_blank')">
                        <i class="fas fa-download"></i> Download Video
                    </button>
                </div>
            `;
        }
    }

    getAvailableFormats(video) {
        const formats = [];

        const primaryUrl = video.videoUrl || video.url;
        if (primaryUrl) {
            formats.push(this.getFileExtension(primaryUrl).toUpperCase());
        }
        if (video.webmUrl) formats.push('WEBM');
        if (video.mp4Url) formats.push('MP4');
        if (video.oggUrl) formats.push('OGG');
        if (video.aviUrl) formats.push('AVI');
        if (video.movUrl) formats.push('MOV');
        if (video.flvUrl) formats.push('FLV');
        if (video.mkvUrl) formats.push('MKV');

        return formats;
    }
    
    displayTags() {
        const videoTags = document.getElementById('videoTags');
        if (!videoTags) return;
        
        videoTags.innerHTML = '';
        
        this.currentVideo.tags.forEach(tag => {
            const tagElement = document.createElement('a');
            tagElement.className = 'tag';
            tagElement.href = '#';
            tagElement.textContent = `#${tag}`;
            tagElement.addEventListener('click', (e) => {
                e.preventDefault();
                this.searchByTag(tag);
            });
            videoTags.appendChild(tagElement);
        });
    }
    
    displayUploaderInfo() {
        const uploaderInfo = document.getElementById('uploaderInfo');
        if (!uploaderInfo) return;
        
        const uploader = this.currentVideo.uploader;
        
        uploaderInfo.innerHTML = `
            <img src="${uploader.avatar}" alt="${uploader.username}" class="uploader-avatar-large">
            <div class="uploader-details">
                <h4>${uploader.username}</h4>
                <p>Uploaded ${this.app.formatDate(this.currentVideo.uploadDate)}</p>
            </div>
        `;
    }
    
    updateActionButtons() {
        const likeBtn = document.getElementById('likeBtn');
        const favoriteBtn = document.getElementById('favoriteBtn');
        
        if (likeBtn) {
            const isLiked = this.app.likes.includes(this.currentVideo.id);
            likeBtn.classList.toggle('active', isLiked);
        }
        
        if (favoriteBtn) {
            const isFavorited = this.app.favorites.includes(this.currentVideo.id);
            favoriteBtn.classList.toggle('active', isFavorited);
        }
    }
    
    setupEventListeners() {
        // Like button
        const likeBtn = document.getElementById('likeBtn');
        if (likeBtn) {
            likeBtn.addEventListener('click', () => {
                const isLiked = this.app.toggleLike(this.currentVideo.id);
                likeBtn.classList.toggle('active', isLiked);
                
                // Update like count (simulate)
                const likeCount = document.getElementById('likeCount');
                if (likeCount) {
                    const currentLikes = parseInt(likeCount.textContent.replace(/[^\d]/g, ''));
                    const newLikes = isLiked ? currentLikes + 1 : currentLikes - 1;
                    likeCount.textContent = this.app.formatNumber(newLikes);
                }
            });
        }
        
        // Favorite button
        const favoriteBtn = document.getElementById('favoriteBtn');
        if (favoriteBtn) {
            favoriteBtn.addEventListener('click', () => {
                const isFavorited = this.app.toggleFavorite(this.currentVideo.id);
                favoriteBtn.classList.toggle('active', isFavorited);
            });
        }
        
        // Share button
        const shareBtn = document.getElementById('shareBtn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.openShareModal();
            });
        }
        
        // Comment form
        const commentForm = document.getElementById('commentForm');
        if (commentForm) {
            commentForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitComment();
            });
        }
        
        // Copy link button
        const copyLinkBtn = document.getElementById('copyLinkBtn');
        if (copyLinkBtn) {
            copyLinkBtn.addEventListener('click', () => {
                this.copyVideoLink();
            });
        }
    }
    
    loadComments() {
        const commentsList = document.getElementById('commentsList');
        if (!commentsList) return;
        
        const comments = this.app.getCommentsForVideo(this.currentVideo.id);
        commentsList.innerHTML = '';
        
        if (comments.length === 0) {
            commentsList.innerHTML = '<p class="no-comments">No comments yet. Be the first to comment!</p>';
            return;
        }
        
        comments.forEach(comment => {
            const commentElement = this.createCommentElement(comment);
            commentsList.appendChild(commentElement);
        });
    }
    
    createCommentElement(comment) {
        const commentDiv = document.createElement('div');
        commentDiv.className = 'comment';
        
        commentDiv.innerHTML = `
            <img src="${comment.userAvatar}" alt="${comment.username}" class="comment-avatar">
            <div class="comment-content">
                <div class="comment-header">
                    <span class="comment-username">${comment.username}</span>
                    <span class="comment-timestamp">${this.app.formatDate(comment.timestamp)}</span>
                </div>
                <p class="comment-text">${comment.comment}</p>
                <div class="comment-actions">
                    <button class="comment-action">👍 ${comment.likes}</button>
                    <button class="comment-action">Reply</button>
                </div>
            </div>
        `;
        
        return commentDiv;
    }
    
    submitComment() {
        const commentInput = document.getElementById('commentInput');
        if (!commentInput) return;
        
        const commentText = commentInput.value.trim();
        if (!commentText) return;
        
        // Create new comment (simulate)
        const newComment = {
            id: 'c' + Date.now(),
            videoId: this.currentVideo.id,
            userId: 'current_user',
            username: 'You',
            userAvatar: 'assets/images/default-avatar.jpg',
            comment: commentText,
            timestamp: new Date().toISOString(),
            likes: 0
        };
        
        // Add to comments array
        this.app.comments.push(newComment);
        
        // Clear input
        commentInput.value = '';
        
        // Reload comments
        this.loadComments();
        
        // Show success message
        this.showSuccessMessage('Comment posted successfully!');
    }
    
    loadRelatedVideos() {
        const relatedVideos = document.getElementById('relatedVideos');
        if (!relatedVideos) return;
        
        const related = this.app.getRelatedVideos(this.currentVideo, 4);
        relatedVideos.innerHTML = '';
        
        related.forEach(video => {
            const videoElement = this.createSidebarVideo(video);
            relatedVideos.appendChild(videoElement);
        });
    }
    
    loadOtherVideos() {
        const otherVideos = document.getElementById('otherVideos');
        if (!otherVideos) return;
        
        // Get random videos excluding current and related
        const relatedIds = this.app.getRelatedVideos(this.currentVideo).map(v => v.id);
        const others = this.app.videos
            .filter(v => v.id !== this.currentVideo.id && !relatedIds.includes(v.id))
            .sort(() => Math.random() - 0.5)
            .slice(0, 4);
        
        otherVideos.innerHTML = '';
        
        others.forEach(video => {
            const videoElement = this.createSidebarVideo(video);
            otherVideos.appendChild(videoElement);
        });
    }
    
    createSidebarVideo(video) {
        const videoDiv = document.createElement('a');
        videoDiv.className = 'sidebar-video';
        videoDiv.href = `video.html?id=${video.id}`;
        
        // Get proper thumbnail with fallback
        const thumbnailSrc = this.app.getThumbnailSrc(video);
        const fallbackThumbnail = this.app.getDefaultThumbnail(video);
        
        videoDiv.innerHTML = `
            <div class="sidebar-video-thumbnail">
                <img src="${thumbnailSrc}" alt="${video.title}" loading="lazy"
                     onerror="this.onerror=null; this.src='${fallbackThumbnail}'"
                     data-video-id="${video.id}">
                <span class="sidebar-video-duration">${video.duration}</span>
            </div>
            <div class="sidebar-video-info">
                <h4 class="sidebar-video-title">${video.title}</h4>
                <div class="sidebar-video-meta">
                    <span>${video.uploader.username}</span>
                    <span>${this.app.formatNumber(video.views)} views</span>
                </div>
            </div>
        `;
        
        return videoDiv;
    }
    
    searchByTag(tag) {
        // Redirect to search results for this tag
        window.location.href = `../index.html?search=${encodeURIComponent(tag)}`;
    }
    
    openShareModal() {
        const shareModal = document.getElementById('shareModal');
        const shareLinkInput = document.getElementById('shareLinkInput');
        
        if (shareModal && shareLinkInput) {
            shareLinkInput.value = window.location.href;
            this.app.openModal('shareModal');
        }
    }
    
    copyVideoLink() {
        const shareLinkInput = document.getElementById('shareLinkInput');
        if (shareLinkInput) {
            shareLinkInput.select();
            document.execCommand('copy');
            this.showSuccessMessage('Link copied to clipboard!');
        }
    }
    
    updatePageTitle() {
        if (this.currentVideo) {
            document.title = `${this.currentVideo.title} - PornTubeX`;
        }
    }
    
    showError(message) {
        console.error(message);
        alert(message);
    }
    
    showSuccessMessage(message) {
        // Simple success message - you could implement a toast system
        console.log(message);
        // For now, just show an alert
        alert(message);
    }
}

// Initialize video player when app is ready
document.addEventListener('DOMContentLoaded', () => {
    const initVideoPlayer = () => {
        if (window.app && window.app.videos.length > 0) {
            new VideoPlayer(window.app);
        } else {
            setTimeout(initVideoPlayer, 100);
        }
    };
    
    initVideoPlayer();
});
