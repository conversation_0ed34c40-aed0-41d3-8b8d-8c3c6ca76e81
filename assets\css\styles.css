/* CSS Variables */
:root {
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --accent-color: #45b7d1;
    --background-dark: #0f0f0f;
    --background-secondary: #1a1a1a;
    --background-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #666666;
    --border-color: #333333;
    --hover-color: #333333;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-dark);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background-color: var(--background-secondary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-brand .brand-link {
    text-decoration: none;
    color: var(--text-primary);
}

.brand-title {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-search {
    flex: 1;
    max-width: 500px;
    margin: 0 2rem;
}

.search-form {
    position: relative;
    display: flex;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    background-color: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2);
}

.search-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.search-btn:hover {
    color: var(--primary-color);
    background-color: var(--hover-color);
}

.nav-menu {
    display: flex;
    gap: 1.5rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background-color: var(--hover-color);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--background-secondary), var(--background-tertiary));
    padding: 4rem 0;
    text-align: center;
    margin-bottom: 3rem;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Sections */
.section-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.categories-section,
.featured-section,
.recent-section {
    margin-bottom: 3rem;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.category-card {
    background-color: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
    color: var(--text-primary);
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.category-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.category-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Videos Grid */
.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Photos Grid */
.photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

/* Photos Section Styling */
.photos-section {
    padding: 2rem 0;
    background-color: var(--background-secondary);
}

.photos-section .section-title {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}



.photo-indicator {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(76, 205, 196, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 4px;
}

.video-card {
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
    color: var(--text-primary);
}

.video-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.05);
}

.video-duration {
    position: absolute;
    bottom: 0.5rem;
    right: 0.5rem;
    background-color: rgba(0, 0, 0, 0.8);
    color: var(--text-primary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
}

.photo-indicator {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background-color: rgba(76, 205, 196, 0.9);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
}

.video-info {
    padding: 1rem;
}

.video-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.uploader-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.uploader-name {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-decoration: none;
}

.uploader-name:hover {
    color: var(--primary-color);
}

.video-stats {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-search {
        order: 3;
        margin: 0;
        width: 100%;
    }
    
    .nav-menu {
        order: 2;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .videos-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 0.8rem;
    }
    
    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .videos-grid {
        grid-template-columns: 1fr;
    }

    .photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.5rem;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .hero-section {
        padding: 2rem 0;
    }
}

/* Video Player Page Styles */
.video-page {
    padding: 1rem 0;
}

.video-layout {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 2rem;
}

.video-player-section {
    min-width: 0;
}

.video-player-container {
    position: relative;
    width: 100%;
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.video-player {
    width: 100%;
    height: auto;
    min-height: 400px;
    background-color: #000;
}

.video-info {
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.video-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.video-stats {
    display: flex;
    gap: 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.video-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.action-btn:hover {
    background-color: var(--hover-color);
    color: var(--text-primary);
}

.action-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.video-description {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
}

.video-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.tag {
    background-color: var(--background-tertiary);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
}

.tag:hover {
    background-color: var(--primary-color);
    color: white;
}

.uploader-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--background-tertiary);
    border-radius: var(--border-radius);
}

.uploader-avatar-large {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.uploader-details h4 {
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.uploader-details p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Comments Section */
.comments-section {
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
}

.comments-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.comment-form {
    margin-bottom: 2rem;
}

.comment-input-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.comment-input {
    width: 100%;
    padding: 1rem;
    background-color: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
}

.comment-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2);
}

.comment-submit-btn {
    align-self: flex-end;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.comment-submit-btn:hover {
    background-color: #ff5252;
}

.comment-submit-btn:disabled {
    background-color: var(--text-muted);
    cursor: not-allowed;
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.comment {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--background-tertiary);
    border-radius: var(--border-radius);
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.comment-username {
    font-weight: 500;
    color: var(--text-primary);
}

.comment-timestamp {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.comment-text {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.comment-actions {
    display: flex;
    gap: 1rem;
}

.comment-action {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.comment-action:hover {
    color: var(--primary-color);
}

/* Sidebar */
.video-sidebar {
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 100px;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.related-videos,
.other-videos {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.sidebar-video {
    display: flex;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: var(--background-tertiary);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
}

.sidebar-video:hover {
    background-color: var(--hover-color);
}

.sidebar-video-thumbnail {
    width: 120px;
    height: 68px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
}

.sidebar-video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sidebar-video-duration {
    position: absolute;
    bottom: 0.25rem;
    right: 0.25rem;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.125rem 0.25rem;
    border-radius: 2px;
    font-size: 0.7rem;
}

.sidebar-video-info {
    flex: 1;
    min-width: 0;
}

.sidebar-video-title {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.sidebar-video-meta {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.modal-close:hover {
    background-color: var(--hover-color);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.share-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.share-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.share-option:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
}

.share-link-container {
    margin-top: 1rem;
}

.share-link-input {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.9rem;
}

/* Category Page Styles */
.category-page {
    padding: 1rem 0;
}

.category-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 0;
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-lg);
}

.category-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.category-description {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.filters-section {
    margin-bottom: 2rem;
}

.filters-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-lg);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-select {
    padding: 0.5rem;
    background-color: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    cursor: pointer;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background-color: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.load-more-container {
    text-align: center;
    margin-top: 2rem;
}

.load-more-btn {
    padding: 0.75rem 2rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.load-more-btn:hover {
    background-color: #ff5252;
}

.no-results {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.no-results h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

/* Footer */
.footer {
    background-color: var(--background-secondary);
    border-top: 1px solid var(--border-color);
    margin-top: 4rem;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Responsive Design for Video Player */
@media (max-width: 1024px) {
    .video-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .video-sidebar {
        position: static;
        order: 2;
    }

    .sidebar-section {
        margin-bottom: 1.5rem;
    }

    .related-videos,
    .other-videos {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
    }

    .sidebar-video {
        flex-direction: column;
    }

    .sidebar-video-thumbnail {
        width: 100%;
        height: 160px;
    }
}

@media (max-width: 768px) {
    .video-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .video-actions {
        flex-wrap: wrap;
    }

    .action-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
        gap: 0.25rem;
    }

    .filter-group:last-child {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .comment-input-container {
        gap: 0.75rem;
    }

    .comment {
        flex-direction: column;
        gap: 0.75rem;
    }

    .comment-avatar {
        align-self: flex-start;
    }
}

@media (max-width: 480px) {
    .video-player {
        min-height: 250px;
    }

    .video-info,
    .comments-section,
    .video-sidebar {
        padding: 1rem;
    }

    .video-title {
        font-size: 1.2rem;
    }

    .video-stats {
        flex-direction: column;
        gap: 0.25rem;
    }

    .video-actions {
        justify-content: center;
    }
}

/* Photo cards styling */
.photo-card {
    background: #1e1e1e;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.photo-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(76, 205, 196, 0.2);
}

/* Photos grid layout */
.photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    padding: 2rem 0;
}

/* Video error handling */
.video-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    background: #1e1e1e;
    border-radius: 8px;
    color: #ffffff;
}

.video-error i {
    font-size: 3rem;
    color: #f44336;
    margin-bottom: 1rem;
}

.video-error h3 {
    color: #ffffff;
    margin-bottom: 1rem;
}

.video-error p {
    color: #b3b3b3;
    margin-bottom: 1.5rem;
}

.format-info {
    background: #2a2a2a;
    padding: 1rem;
    border-radius: 6px;
    margin: 1rem 0;
}

.format-info ul {
    list-style: none;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.format-info li {
    background: #4ecdc4;
    color: #1a1a1a;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.download-btn {
    background: #4ecdc4;
    color: #1a1a1a;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.download-btn:hover {
    background: #45b7aa;
    transform: translateY(-2px);
}

/* Photo viewer styling */
.photo-viewer {
    width: 100%;
    max-width: 100%;
    background: #1a1a1a;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.photo-display {
    width: 100%;
    height: auto;
    max-height: 70vh;
    object-fit: contain;
    display: block;
}

.photo-controls {
    padding: 1rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    background: #2a2a2a;
}

.photo-btn {
    background: #4ecdc4;
    color: #1a1a1a;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.photo-btn:hover {
    background: #45b7aa;
    transform: translateY(-1px);
}

/* Photo modal styles */
.photo-modal {
    z-index: 2000;
}

.photo-modal .modal-content {
    max-width: 95vw;
    max-height: 95vh;
    width: auto;
    height: auto;
    padding: 0;
    background: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
}

.photo-gallery-container {
    display: flex;
    align-items: center;
    position: relative;
    background: #1a1a1a;
}

.photo-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 60vh;
}

.photo-main img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px;
}

.nav-btn {
    background: rgba(76, 205, 196, 0.8);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 1rem;
}

.nav-btn:hover {
    background: rgba(76, 205, 196, 1);
    transform: scale(1.1);
}

.nav-btn:disabled {
    background: rgba(76, 205, 196, 0.3);
    cursor: not-allowed;
    transform: none;
}

.photo-controls {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    justify-content: center;
    background: #2a2a2a;
}

.photo-action-btn {
    background: #4ecdc4;
    color: #1a1a1a;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.photo-action-btn:hover {
    background: #45b7aa;
    transform: translateY(-1px);
}

.photo-info {
    padding: 2rem;
    background: #2a2a2a;
    color: #ffffff;
}

.photo-info h2 {
    margin-bottom: 1rem;
    color: #4ecdc4;
}

.photo-meta {
    display: flex;
    gap: 2rem;
    margin: 1rem 0;
    font-size: 0.9rem;
    color: #b3b3b3;
}

.photo-stats {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: #b3b3b3;
}

.photo-thumbnails {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    background: #2a2a2a;
    overflow-x: auto;
    max-height: 120px;
}

.photo-thumbnail {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.photo-thumbnail.active {
    border-color: #4ecdc4;
}

.photo-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-thumbnail:hover {
    transform: scale(1.05);
}

.thumbnail-title {
    display: none;
}

/* Fullscreen photo modal */
.fullscreen-photo-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.fullscreen-content {
    position: relative;
    max-width: 95vw;
    max-height: 95vh;
}

.fullscreen-content img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.fullscreen-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(76, 205, 196, 0.8);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fullscreen-close:hover {
    background: rgba(76, 205, 196, 1);
}

/* Success notification */
.success-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4caf50;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 4000;
}

.success-notification.show {
    transform: translateX(0);
}

    .action-btn {
        flex: 1;
        justify-content: center;
        min-width: 0;
    }

    .action-btn span {
        display: none;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-header,
    .modal-body {
        padding: 1rem;
    }

    .share-option {
        padding: 1rem;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

.mt-1 {
    margin-top: 0.5rem;
}

.mt-2 {
    margin-top: 1rem;
}

.mt-3 {
    margin-top: 1.5rem;
}

.hidden {
    display: none;
}

.visible {
    display: block;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.skeleton {
    background: linear-gradient(90deg, var(--background-tertiary) 25%, var(--hover-color) 50%, var(--background-tertiary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-tertiary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Photo Modal Styles */
.photo-modal .modal-content {
    max-width: 90vw;
    max-height: 90vh;
    padding: 1rem;
}

.photo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.photo-container img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 10px;
}

.photo-info {
    text-align: center;
    padding: 1rem;
}

.photo-info h2 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.photo-info p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.photo-meta {
    display: flex;
    justify-content: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Photo Card Styles */
.photo-card {
    position: relative;
}

.photo-indicator {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    font-size: 0.8rem;
    z-index: 2;
}

.close-modal {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 2rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: color 0.3s ease;
    background: none;
    border: none;
    z-index: 10;
}

.close-modal:hover {
    color: var(--primary-color);
}

/* Filter Controls Styles */
.filter-section {
    background: var(--card-background);
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.filter-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
    margin-bottom: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-group select {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition);
}

.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 105, 180, 0.2);
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.filter-btn:not(.secondary) {
    background: var(--primary-color);
    color: white;
}

.filter-btn:not(.secondary):hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.filter-btn.secondary {
    background: var(--background-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.filter-btn.secondary:hover {
    background: var(--hover-color);
    color: var(--text-primary);
}

@media (max-width: 768px) {
    .filter-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-btn {
        width: 100%;
    }
}

/* Photo Viewer Styles */
.photo-viewer {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 1rem;
    text-align: center;
}

.photo-display {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.photo-controls {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.photo-btn {
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.photo-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.photo-btn i {
    margin-right: 0.5rem;
}

/* Video Error Styles */
.video-error {
    background: var(--card-background);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    text-align: center;
    color: var(--text-secondary);
}

.video-error i {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.video-error h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.video-error p {
    margin-bottom: 1.5rem;
}

/* Media Error Styles */
.media-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--background-secondary);
    border: 1px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    min-height: 120px;
    color: var(--text-muted);
}

.media-error .error-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.7;
}

.media-error .error-text {
    font-size: 0.9rem;
    text-align: center;
}

.format-info {
    background: var(--background-tertiary);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1.5rem 0;
    text-align: left;
}

.format-info ul {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0;
}

.format-info li {
    padding: 0.25rem 0;
    color: var(--text-primary);
    font-weight: 600;
}

.download-btn {
    padding: 1rem 2rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
}

.download-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.download-btn i {
    margin-right: 0.5rem;
}

/* No Results Styles */
.no-results {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
    background: var(--card-background);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

/* Enhanced Photo Gallery Styles */
.photo-gallery-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.photo-main {
    flex: 1;
    text-align: center;
}

.photo-main img {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.nav-btn:hover:not(:disabled) {
    background: var(--secondary-color);
    transform: scale(1.1);
}

.nav-btn:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.5;
}

.photo-controls {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.photo-action-btn {
    padding: 0.5rem 1rem;
    background: var(--background-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.photo-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.photo-action-btn i {
    margin-right: 0.5rem;
}

.photo-stats {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.photo-thumbnails {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding: 1rem 0;
    max-height: 120px;
}

.photo-thumbnail {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
    position: relative;
}

.photo-thumbnail.active {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.photo-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-thumbnail:hover {
    transform: scale(1.05);
    border-color: var(--secondary-color);
}

.thumbnail-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 0.7rem;
    padding: 0.25rem;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Fullscreen Photo Modal */
.fullscreen-photo-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fullscreen-content {
    position: relative;
    max-width: 95vw;
    max-height: 95vh;
}

.fullscreen-content img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.fullscreen-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0.5rem;
    transition: var(--transition);
}

.fullscreen-close:hover {
    color: var(--primary-color);
    transform: scale(1.2);
}

/* Responsive Design for Photo Gallery */
@media (max-width: 768px) {
    .photo-gallery-container {
        flex-direction: column;
    }

    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .photo-controls {
        gap: 0.25rem;
    }

    .photo-action-btn {
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .photo-thumbnails {
        justify-content: center;
    }

    .photo-thumbnail {
        width: 60px;
        height: 60px;
    }
}

/* Success Notification */
.success-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--success-color, #4CAF50);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 3000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    font-weight: 600;
}

.success-notification.show {
    transform: translateX(0);
}

.success-notification::before {
    content: '✓';
    margin-right: 0.5rem;
    font-weight: bold;
}
