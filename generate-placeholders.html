<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Placeholder Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 40px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .download-all {
            background: #28a745;
            padding: 12px 24px;
            font-size: 16px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Generate Placeholder Images for PornTubeX</h1>
        <p>Click "Generate All" to create placeholder images, then download them individually or all at once.</p>
        
        <button class="download-all" onclick="generateAllImages()">Generate All Images</button>
        
        <div class="section">
            <h2>Video Thumbnails (300x200)</h2>
            <div class="grid" id="thumbnails"></div>
        </div>
        
        <div class="section">
            <h2>User Avatars (50x50)</h2>
            <div class="grid" id="avatars"></div>
        </div>
        
        <div class="section">
            <h2>Small Avatars (24x24)</h2>
            <div class="grid" id="small-avatars"></div>
        </div>
    </div>

    <script>
        const gradients = [
            ['#667eea', '#764ba2'],
            ['#f093fb', '#f5576c'],
            ['#4facfe', '#00f2fe'],
            ['#43e97b', '#38f9d7'],
            ['#fa709a', '#fee140'],
            ['#a8edea', '#fed6e3'],
            ['#ffecd2', '#fcb69f'],
            ['#ff8a80', '#ea80fc']
        ];

        function createGradient(ctx, width, height, colors) {
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, colors[0]);
            gradient.addColorStop(1, colors[1]);
            return gradient;
        }

        function generateThumbnail(index) {
            const canvas = document.createElement('canvas');
            canvas.width = 300;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = createGradient(ctx, 300, 200, gradients[index % gradients.length]);
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 300, 200);
            
            // Add text
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`Video Thumbnail ${index + 1}`, 150, 90);
            ctx.font = '12px Arial';
            ctx.fillText('300x200', 150, 110);
            
            // Add play button
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(150, 130, 20, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#333';
            ctx.beginPath();
            ctx.moveTo(145, 120);
            ctx.lineTo(145, 140);
            ctx.lineTo(160, 130);
            ctx.closePath();
            ctx.fill();
            
            return canvas;
        }

        function generateAvatar(index, size = 50) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = createGradient(ctx, size, size, gradients[index % gradients.length]);
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${Math.floor(size/3)}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(`U${index + 1}`, size/2, size/2 + size/8);
            
            return canvas;
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        function generateAllImages() {
            // Clear existing content
            document.getElementById('thumbnails').innerHTML = '';
            document.getElementById('avatars').innerHTML = '';
            document.getElementById('small-avatars').innerHTML = '';
            
            // Generate thumbnails
            for (let i = 0; i < 6; i++) {
                const canvas = generateThumbnail(i);
                const div = document.createElement('div');
                div.className = 'image-item';
                div.appendChild(canvas);
                
                const button = document.createElement('button');
                button.textContent = `Download thumb${i + 1}.jpg`;
                button.onclick = () => downloadCanvas(canvas, `thumb${i + 1}.jpg`);
                div.appendChild(button);
                
                document.getElementById('thumbnails').appendChild(div);
            }
            
            // Generate avatars
            for (let i = 0; i < 8; i++) {
                const canvas = generateAvatar(i, 50);
                const div = document.createElement('div');
                div.className = 'image-item';
                div.appendChild(canvas);
                
                const button = document.createElement('button');
                button.textContent = `Download avatar${i + 1}.jpg`;
                button.onclick = () => downloadCanvas(canvas, `avatar${i + 1}.jpg`);
                div.appendChild(button);
                
                document.getElementById('avatars').appendChild(div);
            }
            
            // Generate small avatars
            for (let i = 0; i < 4; i++) {
                const canvas = generateAvatar(i, 24);
                const div = document.createElement('div');
                div.className = 'image-item';
                div.appendChild(canvas);
                
                const button = document.createElement('button');
                button.textContent = `Download small${i + 1}.jpg`;
                button.onclick = () => downloadCanvas(canvas, `small-avatar${i + 1}.jpg`);
                div.appendChild(button);
                
                document.getElementById('small-avatars').appendChild(div);
            }
        }

        function downloadAllImages() {
            // This would download all images at once
            const canvases = document.querySelectorAll('canvas');
            canvases.forEach((canvas, index) => {
                setTimeout(() => {
                    downloadCanvas(canvas, `placeholder-${index + 1}.jpg`);
                }, index * 100);
            });
        }
    </script>
</body>
</html>
