// Data adapter to convert TypeScript data to website format
class DataAdapter {
    constructor() {
        this.videoData = [];
        this.photoData = [];
        this.adaptedVideos = [];
        this.adaptedPhotos = [];
    }

    // Load TypeScript data files
    async loadTypeScriptData() {
        try {
            // Since we can't directly import TypeScript in the browser,
            // we'll create JSON versions of our data
            const videoResponse = await fetch('data/video-data.json');
            const photoResponse = await fetch('data/photo-data.json');
            
            if (videoResponse.ok) {
                this.videoData = await videoResponse.json();
            }
            
            if (photoResponse.ok) {
                this.photoData = await photoResponse.json();
            }
            
            this.adaptVideosToWebsiteFormat();
            this.adaptPhotosToWebsiteFormat();
            
        } catch (error) {
            console.error('Error loading TypeScript data:', error);
            // Fallback to empty arrays
            this.videoData = [];
            this.photoData = [];
            this.adaptedVideos = [];
            this.adaptedPhotos = [];
        }
    }

    // Convert video data from TypeScript format to website format
    adaptVideosToWebsiteFormat() {
        this.adaptedVideos = this.videoData.map(video => ({
            id: video.id,
            title: video.title,
            description: video.description,
            thumbnail: video.poster,
            videoUrl: video.url,
            duration: this.extractDurationFromTitle(video.title),
            uploadDate: video.uploadDate || "2025-07-02",
            uploader: {
                id: `user_${video.id}`,
                username: video.user.name,
                avatar: video.user.avatar
            },
            tags: video.tags,
            category: this.getCategoryFromTags(video.tags),
            subcategory: this.getSubcategoryFromTags(video.tags),
            views: this.generateRandomViews(),
            likes: this.generateRandomLikes(),
            rating: this.generateRandomRating(),
            // Additional properties for video formats
            webmUrl: video.webmUrl || "",
            oggUrl: video.oggUrl || "",
            aviUrl: video.aviUrl || "",
            movUrl: video.movUrl || "",
            flvUrl: video.flvUrl || "",
            mkvUrl: video.mkvUrl || "",
            subtitles: video.subtitles || []
        }));
    }

    // Convert photo data to a video-like format for display
    adaptPhotosToWebsiteFormat() {
        this.adaptedPhotos = this.photoData.map(photo => ({
            id: photo.id,
            title: photo.title,
            description: photo.description,
            thumbnail: photo.poster,
            videoUrl: photo.url, // For photos, this will be the image URL
            duration: "Photo",
            uploadDate: photo.uploadDate || "2025-07-02",
            uploader: {
                id: `user_${photo.id}`,
                username: photo.user.name,
                avatar: photo.user.avatar
            },
            tags: [...photo.tags, "photo", "gallery"],
            category: "photos",
            subcategory: "gallery",
            views: this.generateRandomViews(),
            likes: this.generateRandomLikes(),
            rating: this.generateRandomRating(),
            type: "photo",
            width: photo.width,
            height: photo.height,
            fileSize: photo.fileSize
        }));
    }

    // Helper function to extract duration from video title (fallback)
    extractDurationFromTitle(title) {
        // Look for time patterns in title
        const timeMatch = title.match(/(\d{1,2}):(\d{2})/);
        if (timeMatch) {
            return `${timeMatch[1]}:${timeMatch[2]}`;
        }
        
        // Generate random duration for videos without explicit duration
        const minutes = Math.floor(Math.random() * 45) + 5; // 5-50 minutes
        const seconds = Math.floor(Math.random() * 60);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    // Helper function to determine category from tags
    getCategoryFromTags(tags) {
        const categoryMap = {
            'romantic': 'romantic',
            'massage': 'massage',
            'lesbian': 'lesbian',
            'asian': 'asian',
            'latina': 'latina',
            'anal': 'anal',
            'threesome': 'threesome',
            'photo': 'photos'
        };

        for (const tag of tags) {
            if (categoryMap[tag.toLowerCase()]) {
                return categoryMap[tag.toLowerCase()];
            }
        }
        
        return 'adult';
    }

    // Helper function to determine subcategory from tags
    getSubcategoryFromTags(tags) {
        const subcategoryMap = {
            'stepsister': 'family',
            'stepmother': 'family',
            'stepson': 'family',
            'neighbor': 'neighbors',
            'boss': 'workplace',
            'secretary': 'workplace',
            'nurse': 'roleplay',
            'agent': 'roleplay',
            'photo': 'gallery'
        };

        for (const tag of tags) {
            if (subcategoryMap[tag.toLowerCase()]) {
                return subcategoryMap[tag.toLowerCase()];
            }
        }
        
        return 'general';
    }

    // Generate random view count
    generateRandomViews() {
        return Math.floor(Math.random() * 100000) + 1000;
    }

    // Generate random like count
    generateRandomLikes() {
        return Math.floor(Math.random() * 5000) + 100;
    }

    // Generate random rating
    generateRandomRating() {
        return (Math.random() * 2 + 3).toFixed(1); // 3.0 to 5.0
    }

    // Get all adapted videos
    getAdaptedVideos() {
        return this.adaptedVideos;
    }

    // Get all adapted photos
    getAdaptedPhotos() {
        return this.adaptedPhotos;
    }

    // Get combined media (videos + photos)
    getCombinedMedia() {
        return [...this.adaptedVideos, ...this.adaptedPhotos];
    }

    // Get media by type
    getMediaByType(type) {
        if (type === 'video') {
            return this.adaptedVideos;
        } else if (type === 'photo') {
            return this.adaptedPhotos;
        }
        return this.getCombinedMedia();
    }

    // Search function for adapted data
    searchMedia(query, type = 'all') {
        const mediaToSearch = this.getMediaByType(type);
        
        return mediaToSearch.filter(item => 
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.description.toLowerCase().includes(query.toLowerCase()) ||
            item.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase())) ||
            item.uploader.username.toLowerCase().includes(query.toLowerCase())
        );
    }
}

// Export for use in other files
window.DataAdapter = DataAdapter;
