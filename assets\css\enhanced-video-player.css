/* Enhanced Video Player Styles */
.video-container {
    position: relative;
    background: #000;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.custom-video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px 15px 15px;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.progress-container {
    margin-bottom: 10px;
}

.progress-bar {
    position: relative;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    cursor: pointer;
    margin-bottom: 8px;
}

.progress-filled {
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    right: 0;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translate(50%, -50%);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

.controls-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.controls-left,
.controls-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.control-btn svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
    stroke: currentColor;
    stroke-width: 1.5;
}

.play-pause-btn svg {
    width: 24px;
    height: 24px;
}

/* Volume Control */
.volume-container {
    display: flex;
    align-items: center;
    position: relative;
}

.volume-slider {
    width: 0;
    overflow: hidden;
    transition: width 0.3s ease;
}

.volume-container:hover .volume-slider {
    width: 80px;
    margin-left: 5px;
}

.volume-range {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
}

.volume-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.volume-range::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Speed and Quality Menus */
.speed-container,
.quality-container {
    position: relative;
}

.speed-btn,
.quality-btn {
    min-width: 40px;
    font-size: 12px;
    font-weight: 500;
}

.speed-menu,
.quality-menu {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 4px;
    padding: 5px 0;
    margin-bottom: 10px;
    min-width: 80px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.2s ease;
}

.speed-menu.active,
.quality-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.speed-option,
.quality-option {
    padding: 8px 15px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.speed-option:hover,
.quality-option:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.speed-option.active,
.quality-option.active {
    color: var(--primary-color);
    background: rgba(255, 107, 107, 0.1);
}

/* Fullscreen Styles */
.video-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    border-radius: 0;
}

.video-container.fullscreen video {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Loading and Buffering States */
.video-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 14px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .custom-video-controls {
        padding: 15px 10px 10px;
    }
    
    .controls-left,
    .controls-right {
        gap: 5px;
    }
    
    .control-btn {
        padding: 6px;
    }
    
    .control-btn svg {
        width: 18px;
        height: 18px;
    }
    
    .volume-container:hover .volume-slider {
        width: 60px;
    }
    
    .speed-menu,
    .quality-menu {
        right: -10px;
    }
}

@media (max-width: 480px) {
    .time-display {
        font-size: 11px;
    }
    
    .speed-btn,
    .quality-btn {
        min-width: 35px;
        font-size: 11px;
    }
    
    .controls-right {
        gap: 3px;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .volume-slider {
        width: 60px;
        margin-left: 5px;
    }
    
    .progress-handle {
        opacity: 1;
        width: 16px;
        height: 16px;
    }
    
    .control-btn {
        padding: 10px;
        min-width: 44px;
        min-height: 44px;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .custom-video-controls {
        background: rgba(0, 0, 0, 0.95);
    }
    
    .progress-bar {
        background: rgba(255, 255, 255, 0.5);
    }
    
    .control-btn:hover {
        background: rgba(255, 255, 255, 0.2);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .custom-video-controls,
    .progress-filled,
    .progress-handle,
    .speed-menu,
    .quality-menu,
    .volume-slider {
        transition: none;
    }
    
    .loading-spinner {
        animation: none;
    }
}
