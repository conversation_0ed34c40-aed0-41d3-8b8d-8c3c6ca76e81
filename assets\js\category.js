// Category Page functionality
class CategoryPage {
    constructor(app) {
        this.app = app;
        this.currentCategory = null;
        this.categoryId = this.getCategoryIdFromURL();
        this.filteredVideos = [];
        this.currentFilters = {
            sort: 'recent',
            subcategory: 'all',
            duration: 'all'
        };
        this.videosPerPage = 12;
        this.currentPage = 1;
        
        this.init();
    }
    
    async init() {
        // Wait for app to be ready
        if (!this.app.videos.length) {
            setTimeout(() => this.init(), 100);
            return;
        }
        
        if (this.categoryId) {
            this.loadCategory(this.categoryId);
        } else {
            this.showError('Category not found');
        }
        
        this.setupEventListeners();
    }
    
    getCategoryIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('category');
    }
    
    loadCategory(categoryId) {
        this.currentCategory = this.app.getCategoryById(categoryId);
        
        if (!this.currentCategory) {
            this.showError('Category not found');
            return;
        }
        
        this.displayCategoryHeader();
        this.loadSubcategories();
        this.filterAndDisplayVideos();
        this.updatePageTitle();
    }
    
    displayCategoryHeader() {
        const categoryTitle = document.getElementById('categoryTitle');
        const categoryDescription = document.getElementById('categoryDescription');
        
        if (categoryTitle) {
            categoryTitle.textContent = this.currentCategory.name;
        }
        
        if (categoryDescription) {
            categoryDescription.textContent = this.currentCategory.description;
        }
    }
    
    loadSubcategories() {
        const subcategorySelect = document.getElementById('subcategorySelect');
        if (!subcategorySelect) return;
        
        // Clear existing options except "All"
        subcategorySelect.innerHTML = '<option value="all">All</option>';
        
        this.currentCategory.subcategories.forEach(subcategory => {
            const option = document.createElement('option');
            option.value = subcategory.id;
            option.textContent = subcategory.name;
            subcategorySelect.appendChild(option);
        });
    }
    
    setupEventListeners() {
        // Sort filter
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentFilters.sort = e.target.value;
                this.currentPage = 1;
                this.filterAndDisplayVideos();
            });
        }
        
        // Subcategory filter
        const subcategorySelect = document.getElementById('subcategorySelect');
        if (subcategorySelect) {
            subcategorySelect.addEventListener('change', (e) => {
                this.currentFilters.subcategory = e.target.value;
                this.currentPage = 1;
                this.filterAndDisplayVideos();
            });
        }
        
        // Duration filters
        const durationButtons = document.querySelectorAll('.filter-btn[data-duration]');
        durationButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // Remove active class from all duration buttons
                durationButtons.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to clicked button
                e.target.classList.add('active');
                
                this.currentFilters.duration = e.target.dataset.duration;
                this.currentPage = 1;
                this.filterAndDisplayVideos();
            });
        });
        
        // Load more button
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.currentPage++;
                this.filterAndDisplayVideos(true);
            });
        }
    }
    
    filterAndDisplayVideos(append = false) {
        // Get videos for this category
        let videos = this.app.getVideosByCategory(this.categoryId);
        
        // Apply subcategory filter
        if (this.currentFilters.subcategory !== 'all') {
            videos = videos.filter(video => video.subcategory === this.currentFilters.subcategory);
        }
        
        // Apply duration filter
        if (this.currentFilters.duration !== 'all') {
            videos = videos.filter(video => {
                const duration = this.parseDuration(video.duration);
                switch (this.currentFilters.duration) {
                    case 'short':
                        return duration < 600; // Under 10 minutes
                    case 'medium':
                        return duration >= 600 && duration <= 1200; // 10-20 minutes
                    case 'long':
                        return duration > 1200; // Over 20 minutes
                    default:
                        return true;
                }
            });
        }
        
        // Apply sorting
        videos = this.sortVideos(videos, this.currentFilters.sort);
        
        this.filteredVideos = videos;
        
        // Paginate videos
        const startIndex = (this.currentPage - 1) * this.videosPerPage;
        const endIndex = startIndex + this.videosPerPage;
        const videosToShow = videos.slice(0, endIndex);
        
        this.displayVideos(videosToShow, append);
        this.updateLoadMoreButton(endIndex < videos.length);
        this.updateNoResults(videos.length === 0);
    }
    
    sortVideos(videos, sortBy) {
        switch (sortBy) {
            case 'recent':
                return videos.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));
            case 'popular':
                return videos.sort((a, b) => b.likes - a.likes);
            case 'views':
                return videos.sort((a, b) => b.views - a.views);
            case 'rating':
                return videos.sort((a, b) => b.rating - a.rating);
            default:
                return videos;
        }
    }
    
    parseDuration(duration) {
        // Parse duration string (e.g., "12:34") to seconds
        const parts = duration.split(':');
        const minutes = parseInt(parts[0]) || 0;
        const seconds = parseInt(parts[1]) || 0;
        return minutes * 60 + seconds;
    }
    
    displayVideos(videos, append = false) {
        const categoryVideos = document.getElementById('categoryVideos');
        if (!categoryVideos) return;
        
        if (!append) {
            categoryVideos.innerHTML = '';
        }
        
        videos.forEach(video => {
            const videoCard = this.app.createVideoCard(video);
            categoryVideos.appendChild(videoCard);
        });
    }
    
    updateLoadMoreButton(hasMore) {
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        const loadMoreContainer = document.querySelector('.load-more-container');
        
        if (loadMoreContainer) {
            loadMoreContainer.style.display = hasMore ? 'block' : 'none';
        }
        
        if (loadMoreBtn) {
            loadMoreBtn.disabled = !hasMore;
            loadMoreBtn.textContent = hasMore ? 'Load More Videos' : 'No More Videos';
        }
    }
    
    updateNoResults(show) {
        const noResults = document.getElementById('noResults');
        const videosSection = document.querySelector('.videos-section');
        
        if (noResults) {
            noResults.style.display = show ? 'block' : 'none';
        }
        
        if (videosSection) {
            videosSection.style.display = show ? 'none' : 'block';
        }
    }
    
    updatePageTitle() {
        if (this.currentCategory) {
            document.title = `${this.currentCategory.name} - PornTubeX`;
        }
    }
    
    showError(message) {
        console.error(message);
        alert(message);
    }
}

// Initialize category page when app is ready
document.addEventListener('DOMContentLoaded', () => {
    const initCategoryPage = () => {
        if (window.app && window.app.videos.length > 0) {
            new CategoryPage(window.app);
        } else {
            setTimeout(initCategoryPage, 100);
        }
    };
    
    initCategoryPage();
});
