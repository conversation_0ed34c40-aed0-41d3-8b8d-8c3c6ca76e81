Write-Host "Checking video file extensions..." -ForegroundColor Green

$videoDir = "E:\html project\categories\Videos"

# Check a few key videos manually
$testVideos = @(
    "OH FUCK! I want to Cum all over your Cock! - Pornhub.com",
    "I CREAMPIED my Bestie from Bangladesh 🇧🇩 - Pornhub.com", 
    "LEZ BE BAD - Oiled up GFs Sarah Arabic & little Puck ORGASM during ROUGH LESBIAN SCISSORING SESH! - Pornhub.com_2"
)

foreach ($video in $testVideos) {
    $mp4Path = "$videoDir\$video.mp4"
    $tsPath = "$videoDir\$video.ts"
    
    if (Test-Path $mp4Path) {
        Write-Host "MP4 EXISTS: $video" -ForegroundColor Green
    } elseif (Test-Path $tsPath) {
        Write-Host "TS EXISTS: $video" -ForegroundColor Yellow
    } else {
        Write-Host "MISSING: $video" -ForegroundColor Red
    }
}
