# Media Loading Configuration for Apache Servers
# This file helps resolve common media loading issues on hosting servers

# Enable MIME type for video files
AddType video/mp4 .mp4
AddType video/mp2t .ts
AddType video/webm .webm
AddType image/webp .webp
AddType image/jpeg .jpg
AddType image/png .png

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/css text/javascript application/javascript application/json
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Set cache headers for media files to improve loading speed
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType video/mp4 "access plus 1 month"
    ExpiresByType video/mp2t "access plus 1 month"
    ExpiresByType video/webm "access plus 1 month"
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
</IfModule>

# Allow access to JSON data files
<Files "*.json">
    Order allow,deny
    Allow from all
</Files>

# Prevent access to sensitive files
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# Enable CORS for media files if needed
<IfModule mod_headers.c>
    <FilesMatch "\.(mp4|ts|webm|jpg|jpeg|png|webp|gif)$">
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
</IfModule>

# Handle URL encoding for files with special characters
Options +FollowSymLinks
RewriteEngine On

# Redirect common 404 errors to index page
ErrorDocument 404 /index.html

# Force UTF-8 encoding
AddDefaultCharset UTF-8

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
