// Node.js script to convert TypeScript data files to JSON
const fs = require('fs');
const path = require('path');

// Read and parse TypeScript files
function convertTsToJson() {
    try {
        // Read videodata.ts
        const videoTsContent = fs.readFileSync('videodata.ts', 'utf8');
        
        // Extract the array data from TypeScript file
        const videoArrayMatch = videoTsContent.match(/export const videoData: MediaItem\[\] = (\[[\s\S]*\]);/);
        if (videoArrayMatch) {
            // Clean up the TypeScript syntax to make it valid JSON
            let videoJsonString = videoArrayMatch[1]
                .replace(/(\w+):/g, '"$1":')  // Add quotes around property names
                .replace(/'/g, '"')           // Replace single quotes with double quotes
                .replace(/,(\s*[}\]])/g, '$1'); // Remove trailing commas
            
            const videoData = JSON.parse(videoJsonString);
            
            // Write to JSON file
            fs.writeFileSync('data/video-data.json', JSON.stringify(videoData, null, 2));
            console.log(`✓ Converted ${videoData.length} videos to data/video-data.json`);
        }

        // Read photodata.ts
        const photoTsContent = fs.readFileSync('photodata.ts', 'utf8');
        
        // Extract the array data from TypeScript file
        const photoArrayMatch = photoTsContent.match(/export const photoData: PhotoItem\[\] = (\[[\s\S]*\]);/);
        if (photoArrayMatch) {
            // Clean up the TypeScript syntax to make it valid JSON
            let photoJsonString = photoArrayMatch[1]
                .replace(/(\w+):/g, '"$1":')  // Add quotes around property names
                .replace(/'/g, '"')           // Replace single quotes with double quotes
                .replace(/,(\s*[}\]])/g, '$1'); // Remove trailing commas
            
            const photoData = JSON.parse(photoJsonString);
            
            // Write to JSON file
            fs.writeFileSync('data/photo-data.json', JSON.stringify(photoData, null, 2));
            console.log(`✓ Converted ${photoData.length} photos to data/photo-data.json`);
        }

    } catch (error) {
        console.error('Error converting TypeScript to JSON:', error);
    }
}

// Create data directory if it doesn't exist
if (!fs.existsSync('data')) {
    fs.mkdirSync('data');
}

convertTsToJson();
