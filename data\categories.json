{"categories": [{"id": "romantic", "name": "Romantic", "description": "Beautiful romantic content for couples", "icon": "💕", "subcategories": [{"id": "couples", "name": "<PERSON><PERSON><PERSON>", "description": "Content featuring couples"}, {"id": "intimate", "name": "Intimate", "description": "Intimate romantic moments"}]}, {"id": "massage", "name": "Massage", "description": "Relaxing and therapeutic massage content", "icon": "💆", "subcategories": [{"id": "sensual", "name": "Sensual", "description": "Sensual massage techniques"}, {"id": "therapeutic", "name": "Therapeutic", "description": "Therapeutic massage methods"}]}, {"id": "artistic", "name": "Artistic", "description": "Artistic expression and beauty", "icon": "🎨", "subcategories": [{"id": "photography", "name": "Photography", "description": "Artistic photography content"}, {"id": "dance", "name": "Dance", "description": "Expressive dance and movement"}]}, {"id": "wellness", "name": "Wellness", "description": "Health and wellness content", "icon": "🧘", "subcategories": [{"id": "meditation", "name": "Meditation", "description": "Meditation and mindfulness"}, {"id": "yoga", "name": "Yoga", "description": "Yoga and stretching"}]}]}