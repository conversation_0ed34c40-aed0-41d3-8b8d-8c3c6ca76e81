# Check remaining videos that might need .ts to .mp4 updates
$videoDir = "E:\html project\categories\Videos"

Write-Host "Checking videos that might still need URL updates..." -ForegroundColor Cyan

# These are videos I suspect might have been converted but aren't updated yet
$testVideos = @(
    # Non-English titles that might have been converted
    "パイズリ手コキ集 - Pornhub.com",
    "元グラビア出身のiカップ爆乳の医療学.せ.いあいりちゃん。 - Pornhub.com",
    "長い間セックスをしてなかったから何度も中出しと顔射で精液まみれになって妊娠しちゃうかもしれない❤️ - Pornhub.com"
)

foreach ($video in $testVideos) {
    $mp4Path = "$videoDir\$video.mp4"
    $tsPath = "$videoDir\$video.ts"
    
    Write-Host "`nTesting: $video" -ForegroundColor White
    
    if (Test-Path $mp4Path) {
        Write-Host "  ✓ MP4 EXISTS - NEEDS CODE UPDATE" -ForegroundColor Green
    } elseif (Test-Path $tsPath) {
        Write-Host "  ○ TS EXISTS - Code is correct" -ForegroundColor Yellow
    } else {
        Write-Host "  ✗ MISSING BOTH - Check filename" -ForegroundColor Red
    }
}

Write-Host "`n=== Summary ===" -ForegroundColor Cyan
Write-Host "Check the results above to see which videos need URL updates in main.js" -ForegroundColor White
