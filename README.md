# PornTubeX - Static Adult Content Website

A modern, responsive static website for adult content built with HTML5, CSS3, and vanilla JavaScript.

## Features

### 🎨 Modern Design
- Dark theme with gradient accents
- Responsive design for desktop and mobile
- Smooth animations and transitions
- Professional typography using Inter font

### 📱 Responsive Layout
- Mobile-first design approach
- CSS Grid and Flexbox layouts
- Optimized for all screen sizes
- Touch-friendly interface

### 🎥 Video Features
- Video player with custom controls
- Video metadata display (title, description, tags)
- Uploader profile information
- Related videos recommendations
- Duration and view count display

### 💬 Interactive Comments
- Comment submission form with validation
- Display existing comments with timestamps
- User avatars and usernames
- Like functionality for comments

### 🔍 Search & Navigation
- Real-time search functionality
- Category-based browsing
- Tag-based filtering
- Sort options (recent, popular, views, rating)

### ❤️ User Interactions
- Like/favorite functionality with localStorage
- Share video functionality
- Clickable tags for filtering
- Persistent user preferences

### 📊 Content Organization
- Category and subcategory system
- Video recommendations based on tags
- Featured and recent video sections
- Load more pagination

## Project Structure

```
PornTubeX/
├── index.html                 # Homepage
├── pages/
│   ├── video.html            # Video player page
│   └── category.html         # Category browsing page
├── assets/
│   ├── css/
│   │   └── styles.css        # Main stylesheet
│   ├── js/
│   │   ├── main.js           # Core application logic
│   │   ├── homepage.js       # Homepage functionality
│   │   ├── video-player.js   # Video player functionality
│   │   └── category.js       # Category page functionality
│   └── images/               # Image assets
├── data/
│   ├── videos.json           # Video metadata
│   ├── users.json            # User profiles
│   ├── comments.json         # Comments data
│   └── categories.json       # Category definitions
└── generate-placeholders.html # Utility for generating placeholder images
```

## Sample Data

The website includes comprehensive sample data:

- **6 sample videos** with metadata, thumbnails, and video URLs
- **8 user profiles** with avatars and profile information
- **8 sample comments** with user associations and timestamps
- **4 content categories** with subcategories (Romantic, Massage, Artistic, Wellness)

## Technical Implementation

### HTML5 Features
- Semantic markup structure
- Video element with controls
- Form validation
- Accessible navigation

### CSS3 Features
- CSS Custom Properties (variables)
- CSS Grid and Flexbox
- Responsive media queries
- Smooth transitions and animations
- Modern gradient backgrounds

### JavaScript Features
- ES6+ syntax and features
- Modular class-based architecture
- LocalStorage for data persistence
- Fetch API for data loading
- Event delegation and handling
- Error handling and validation

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Setup Instructions

1. **Clone or download** the project files
2. **Open index.html** in a web browser
3. **For local development**, use a local server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

## Usage

### Homepage
- Browse featured and recent videos
- Navigate through categories
- Use the search functionality

### Video Player
- Click any video to open the player page
- Like/favorite videos (stored in localStorage)
- Add comments (simulated functionality)
- Share videos via copy link
- Browse related videos in sidebar

### Category Pages
- Filter videos by category and subcategory
- Sort by different criteria
- Filter by video duration
- Load more videos with pagination

## Customization

### Adding New Videos
Edit `data/videos.json` to add new video entries with the following structure:
```json
{
  "id": "unique_id",
  "title": "Video Title",
  "description": "Video description",
  "thumbnail": "thumbnail_url",
  "videoUrl": "video_url",
  "duration": "MM:SS",
  "uploadDate": "YYYY-MM-DD",
  "uploader": {
    "id": "user_id",
    "username": "username",
    "avatar": "avatar_url"
  },
  "tags": ["tag1", "tag2"],
  "category": "category_id",
  "subcategory": "subcategory_id",
  "views": 0,
  "likes": 0,
  "rating": 0.0
}
```

### Styling Customization
Modify CSS variables in `assets/css/styles.css`:
```css
:root {
  --primary-color: #ff6b6b;
  --secondary-color: #4ecdc4;
  --background-dark: #0f0f0f;
  /* ... other variables */
}
```

## Performance Considerations

- Lazy loading for images
- Efficient CSS Grid layouts
- Minimal JavaScript bundle size
- Optimized placeholder images
- LocalStorage for client-side data

## Security Notes

This is a static demonstration website. For production use:
- Implement proper authentication
- Add server-side validation
- Use HTTPS for all content
- Implement content moderation
- Add rate limiting for interactions

## License

This project is for demonstration purposes only. Please ensure compliance with local laws and regulations when adapting for actual use.
