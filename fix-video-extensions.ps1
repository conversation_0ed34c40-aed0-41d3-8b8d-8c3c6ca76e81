# PowerShell script to check which videos exist as .mp4 vs .ts and report the findings

$videoDir = "E:\html project\categories\Videos"
$codeFile = "E:\html project\assets\js\main.js"

Write-Host "Checking video file extensions..." -ForegroundColor Green

# List of videos that currently have .ts in the code but might be .mp4 on disk
$videosToCheck = @(
    "OH FUCK! I want to Cum all over your Cock! - Pornhub.com",
    "My Stepmother Discovers how Inexperienced I am and Volunteers to Teach Me. - Pornhub.com_2",
    "Full Video - PropertySex Horny Agent with Huge Natural Tits Makes Sex Video with Homebuyer",
    "I CREAMPIED my Bestie from Bangladesh 🇧🇩 - Pornhub.com",
    "I've been Hinting to you all Day! Well, Fuck me Already! - Dolly Rud - Pornhub.com",
    "Indian Village Romantic Sex with Desi Girlfriend Full Hindi Video - Pornhub.com",
    "Iraqi Cheating Wife got CREAMPIED - Pornhub.com",
    "LEZ BE BAD - Oiled up GFs Sarah Arabic & little Puck ORGASM during ROUGH LESBIAN SCISSORING SESH! - Pornhub.com_2",
    "Little StepSister SNEAKS into my Room... FORGOT TO WEAR PANTIES - Pornhub.com",
    "MY STEPSISTER HELPS ME LOSE MY VIRGINITY - Pornhub.com",
    "Miho Ichiki Shows off Big Tits before taking Hardcore Creampie with Passion - Pornhub.com",
    "My 18-Year-Old Step-Sister wants to Feel my Cock inside Her💦 - Pornhub.com",
    "My Pakistani Nurse want Fucked in Bedroom-Clear Hindi Audio 💦 - Pornhub.com",
    "PUSSY TO ASS TO SQUIRT TO CUM!! SPECIAL - Pornhub.com",
    "PropertySex Desperate Agent looking to get a Quick Deal from the Horny Home Seller - Pornhub.com",
    "Public Agent Happy Pakistani Woman Fucks a Stranger in the Public Toilets - Pornhub.com",
    "Sharing a Bed with my best Friends HOT Girlfriend - Pornhub.com",
    "Sharing the Bed with Big Titty Step Sis - Pornhub.com",
    "Step Sister's Anal Obsession. - Pornhub.com",
    "Studying with Thick Asian Step Sister - Suki Sin - Family Therapy - Alex Adams - Pornhub.com",
    "Submissive Receives more than just an Oily Massage. - Pornhub.com",
    "TRY TO GET ORGASM AND CREAM CUM AGIN ON WEBCAM - Pornhub.com",
    "VIXENPLUS Japanese Journalist vs the BIGGEST BBC IN THE WORLD - Pornhub.com",
    "WOWGIRLS Jia Lissa and Lena Reif have Incredibly Hot Sex on their first Lesbian Date. - Pornhub.com",
    "We got too Slippery Playing with Lotion… Ended up Creampied. - Pornhub.com",
    "Wow! I didn't think my Stepsister's Huge Ass would Turn me On. - Pornhub.com",
    "is it Opening Up. - Step Sister Lends me her Ass, Part 4 - Pornhub.com"
)

$mp4Files = @()
$tsFiles = @()
$missingFiles = @()

foreach ($video in $videosToCheck) {
    $mp4Path = Join-Path $videoDir "$video.mp4"
    $tsPath = Join-Path $videoDir "$video.ts"
    
    if (Test-Path $mp4Path) {
        $mp4Files += $video
        Write-Host "✓ MP4: $video" -ForegroundColor Green
    } elseif (Test-Path $tsPath) {
        $tsFiles += $video
        Write-Host "○ TS: $video" -ForegroundColor Yellow
    } else {
        $missingFiles += $video
        Write-Host "✗ MISSING: $video" -ForegroundColor Red
    }
}

Write-Host "`n=== SUMMARY ===" -ForegroundColor Cyan
Write-Host "MP4 files (need code update): $($mp4Files.Count)" -ForegroundColor Green
Write-Host "TS files (code is correct): $($tsFiles.Count)" -ForegroundColor Yellow  
Write-Host "Missing files (need attention): $($missingFiles.Count)" -ForegroundColor Red

if ($mp4Files.Count -gt 0) {
    Write-Host "`n=== FILES TO UPDATE IN CODE (.ts to .mp4) ===" -ForegroundColor Green
    foreach ($file in $mp4Files) {
        Write-Host "  - $file" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "`n=== MISSING FILES ===" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
}
