<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2a2a2a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="300" height="200" fill="url(#bg)"/>
  <circle cx="120" cy="80" r="20" fill="#ff6b6b" opacity="0.8"/>
  <circle cx="180" cy="80" r="20" fill="#ff6b6b" opacity="0.8"/>
  <circle cx="150" cy="120" r="20" fill="#ff6b6b" opacity="0.8"/>
  <polygon points="110,75 110,85 125,80" fill="white"/>
  <polygon points="170,75 170,85 185,80" fill="white"/>
  <polygon points="140,115 140,125 155,120" fill="white"/>
  <text x="150" y="160" text-anchor="middle" fill="#b3b3b3" font-family="Arial, sans-serif" font-size="12">Threesome</text>
</svg>
