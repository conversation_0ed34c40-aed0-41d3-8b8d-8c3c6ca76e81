<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Placeholder Image Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            margin: 0;
            padding: 20px;
        }
        
        .placeholder {
            display: inline-block;
            margin: 10px;
            text-align: center;
            color: white;
            font-weight: bold;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .thumbnail {
            width: 300px;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .avatar-small {
            width: 24px;
            height: 24px;
            font-size: 8px;
        }
        
        .avatar-large {
            width: 80px;
            height: 80px;
            font-size: 16px;
        }
        
        h2 {
            color: #333;
            margin-top: 30px;
        }
        
        .grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
    </style>
</head>
<body>
    <h1>Placeholder Images for PornTubeX</h1>
    <p>Right-click and save these placeholder images for the website.</p>
    
    <h2>Video Thumbnails</h2>
    <div class="grid">
        <div class="placeholder thumbnail" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            Video Thumbnail 1<br>300x200
        </div>
        <div class="placeholder thumbnail" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            Video Thumbnail 2<br>300x200
        </div>
        <div class="placeholder thumbnail" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            Video Thumbnail 3<br>300x200
        </div>
        <div class="placeholder thumbnail" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            Video Thumbnail 4<br>300x200
        </div>
        <div class="placeholder thumbnail" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
            Video Thumbnail 5<br>300x200
        </div>
        <div class="placeholder thumbnail" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
            Video Thumbnail 6<br>300x200
        </div>
    </div>
    
    <h2>User Avatars</h2>
    <div class="grid">
        <div class="placeholder avatar" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            U1
        </div>
        <div class="placeholder avatar" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            U2
        </div>
        <div class="placeholder avatar" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            U3
        </div>
        <div class="placeholder avatar" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            U4
        </div>
        <div class="placeholder avatar" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
            U5
        </div>
        <div class="placeholder avatar" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
            U6
        </div>
        <div class="placeholder avatar" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
            U7
        </div>
        <div class="placeholder avatar" style="background: linear-gradient(135deg, #ff8a80 0%, #ea80fc 100%);">
            U8
        </div>
    </div>
    
    <h2>Small Avatars (24x24)</h2>
    <div class="grid">
        <div class="placeholder avatar avatar-small" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            1
        </div>
        <div class="placeholder avatar avatar-small" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            2
        </div>
        <div class="placeholder avatar avatar-small" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            3
        </div>
        <div class="placeholder avatar avatar-small" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            4
        </div>
    </div>
    
    <script>
        // Instructions for saving images
        document.addEventListener('DOMContentLoaded', function() {
            const placeholders = document.querySelectorAll('.placeholder');
            placeholders.forEach((placeholder, index) => {
                placeholder.addEventListener('contextmenu', function(e) {
                    console.log('Right-click to save this placeholder image');
                });
            });
        });
    </script>
</body>
</html>
