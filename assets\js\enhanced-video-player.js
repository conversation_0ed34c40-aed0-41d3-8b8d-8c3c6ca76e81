// Enhanced Video Player with Custom Controls
class EnhancedVideoPlayer {
    constructor(videoElement, options = {}) {
        this.video = videoElement;
        this.container = videoElement.parentElement;
        this.options = {
            autoplay: false,
            controls: true,
            keyboard: true,
            pip: true,
            quality: true,
            speed: true,
            ...options
        };
        
        this.isPlaying = false;
        this.isMuted = false;
        this.isFullscreen = false;
        this.currentQuality = 'auto';
        this.playbackRate = 1;
        
        this.init();
    }
    
    init() {
        this.createCustomControls();
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.hideDefaultControls();
    }
    
    hideDefaultControls() {
        this.video.controls = false;
    }
    
    createCustomControls() {
        const controlsHTML = `
            <div class="custom-video-controls">
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-filled"></div>
                        <div class="progress-handle"></div>
                    </div>
                    <div class="time-display">
                        <span class="current-time">0:00</span>
                        <span class="duration">0:00</span>
                    </div>
                </div>
                
                <div class="controls-row">
                    <div class="controls-left">
                        <button class="control-btn play-pause-btn">
                            <svg class="play-icon" viewBox="0 0 24 24">
                                <polygon points="5,3 19,12 5,21"></polygon>
                            </svg>
                            <svg class="pause-icon" viewBox="0 0 24 24" style="display: none;">
                                <rect x="6" y="4" width="4" height="16"></rect>
                                <rect x="14" y="4" width="4" height="16"></rect>
                            </svg>
                        </button>
                        
                        <div class="volume-container">
                            <button class="control-btn volume-btn">
                                <svg class="volume-icon" viewBox="0 0 24 24">
                                    <polygon points="11,5 6,9 2,9 2,15 6,15 11,19"></polygon>
                                    <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                                </svg>
                            </button>
                            <div class="volume-slider">
                                <input type="range" min="0" max="100" value="100" class="volume-range">
                            </div>
                        </div>
                    </div>
                    
                    <div class="controls-right">
                        ${this.options.speed ? `
                            <div class="speed-container">
                                <button class="control-btn speed-btn">1x</button>
                                <div class="speed-menu">
                                    <div class="speed-option" data-speed="0.25">0.25x</div>
                                    <div class="speed-option" data-speed="0.5">0.5x</div>
                                    <div class="speed-option" data-speed="0.75">0.75x</div>
                                    <div class="speed-option active" data-speed="1">1x</div>
                                    <div class="speed-option" data-speed="1.25">1.25x</div>
                                    <div class="speed-option" data-speed="1.5">1.5x</div>
                                    <div class="speed-option" data-speed="2">2x</div>
                                </div>
                            </div>
                        ` : ''}
                        
                        ${this.options.quality ? `
                            <div class="quality-container">
                                <button class="control-btn quality-btn">Auto</button>
                                <div class="quality-menu">
                                    <div class="quality-option active" data-quality="auto">Auto</div>
                                    <div class="quality-option" data-quality="1080p">1080p</div>
                                    <div class="quality-option" data-quality="720p">720p</div>
                                    <div class="quality-option" data-quality="480p">480p</div>
                                </div>
                            </div>
                        ` : ''}
                        
                        ${this.options.pip ? `
                            <button class="control-btn pip-btn">
                                <svg viewBox="0 0 24 24">
                                    <rect x="3" y="3" width="18" height="14" rx="2" ry="2"></rect>
                                    <rect x="9" y="9" width="10" height="8" rx="1" ry="1"></rect>
                                </svg>
                            </button>
                        ` : ''}
                        
                        <button class="control-btn fullscreen-btn">
                            <svg class="expand-icon" viewBox="0 0 24 24">
                                <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>
                            </svg>
                            <svg class="compress-icon" viewBox="0 0 24 24" style="display: none;">
                                <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        this.container.insertAdjacentHTML('beforeend', controlsHTML);
        this.controls = this.container.querySelector('.custom-video-controls');
        
        // Cache control elements
        this.playPauseBtn = this.controls.querySelector('.play-pause-btn');
        this.progressBar = this.controls.querySelector('.progress-bar');
        this.progressFilled = this.controls.querySelector('.progress-filled');
        this.currentTimeEl = this.controls.querySelector('.current-time');
        this.durationEl = this.controls.querySelector('.duration');
        this.volumeBtn = this.controls.querySelector('.volume-btn');
        this.volumeRange = this.controls.querySelector('.volume-range');
        this.fullscreenBtn = this.controls.querySelector('.fullscreen-btn');
        
        if (this.options.speed) {
            this.speedBtn = this.controls.querySelector('.speed-btn');
            this.speedMenu = this.controls.querySelector('.speed-menu');
        }
        
        if (this.options.quality) {
            this.qualityBtn = this.controls.querySelector('.quality-btn');
            this.qualityMenu = this.controls.querySelector('.quality-menu');
        }
        
        if (this.options.pip) {
            this.pipBtn = this.controls.querySelector('.pip-btn');
        }
    }
    
    setupEventListeners() {
        // Video events
        this.video.addEventListener('loadedmetadata', () => this.updateDuration());
        this.video.addEventListener('timeupdate', () => this.updateProgress());
        this.video.addEventListener('play', () => this.onPlay());
        this.video.addEventListener('pause', () => this.onPause());
        this.video.addEventListener('ended', () => this.onEnded());
        this.video.addEventListener('volumechange', () => this.updateVolumeDisplay());
        
        // Control events
        this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        this.progressBar.addEventListener('click', (e) => this.seek(e));
        this.volumeBtn.addEventListener('click', () => this.toggleMute());
        this.volumeRange.addEventListener('input', (e) => this.setVolume(e.target.value / 100));
        this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        
        // Speed control
        if (this.options.speed) {
            this.speedBtn.addEventListener('click', () => this.toggleSpeedMenu());
            this.speedMenu.addEventListener('click', (e) => this.setPlaybackRate(e));
        }
        
        // Quality control
        if (this.options.quality) {
            this.qualityBtn.addEventListener('click', () => this.toggleQualityMenu());
            this.qualityMenu.addEventListener('click', (e) => this.setQuality(e));
        }
        
        // Picture-in-Picture
        if (this.options.pip && 'pictureInPictureEnabled' in document) {
            this.pipBtn.addEventListener('click', () => this.togglePiP());
        }
        
        // Hide controls on mouse leave
        this.container.addEventListener('mouseenter', () => this.showControls());
        this.container.addEventListener('mouseleave', () => this.hideControls());
        this.container.addEventListener('mousemove', () => this.showControls());
    }
    
    setupKeyboardShortcuts() {
        if (!this.options.keyboard) return;
        
        document.addEventListener('keydown', (e) => {
            if (!this.isVideoFocused()) return;
            
            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlayPause();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.seek(null, -10);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.seek(null, 10);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.adjustVolume(0.1);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.adjustVolume(-0.1);
                    break;
                case 'KeyF':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'KeyM':
                    e.preventDefault();
                    this.toggleMute();
                    break;
            }
        });
    }
    
    // Core playback methods
    togglePlayPause() {
        if (this.video.paused) {
            this.video.play();
        } else {
            this.video.pause();
        }
    }
    
    onPlay() {
        this.isPlaying = true;
        this.playPauseBtn.querySelector('.play-icon').style.display = 'none';
        this.playPauseBtn.querySelector('.pause-icon').style.display = 'block';
    }
    
    onPause() {
        this.isPlaying = false;
        this.playPauseBtn.querySelector('.play-icon').style.display = 'block';
        this.playPauseBtn.querySelector('.pause-icon').style.display = 'none';
    }
    
    onEnded() {
        this.onPause();
        // Could trigger auto-play next video here
    }
    
    // Progress and seeking
    updateProgress() {
        const progress = (this.video.currentTime / this.video.duration) * 100;
        this.progressFilled.style.width = `${progress}%`;
        this.currentTimeEl.textContent = this.formatTime(this.video.currentTime);
    }
    
    updateDuration() {
        this.durationEl.textContent = this.formatTime(this.video.duration);
    }
    
    seek(event, offset = null) {
        if (offset !== null) {
            // Keyboard seeking
            this.video.currentTime = Math.max(0, Math.min(this.video.duration, this.video.currentTime + offset));
        } else {
            // Click seeking
            const rect = this.progressBar.getBoundingClientRect();
            const pos = (event.clientX - rect.left) / rect.width;
            this.video.currentTime = pos * this.video.duration;
        }
    }
    
    // Volume control
    toggleMute() {
        this.video.muted = !this.video.muted;
        this.updateVolumeDisplay();
    }
    
    setVolume(volume) {
        this.video.volume = Math.max(0, Math.min(1, volume));
        this.video.muted = volume === 0;
    }
    
    adjustVolume(delta) {
        this.setVolume(this.video.volume + delta);
        this.volumeRange.value = this.video.volume * 100;
    }
    
    updateVolumeDisplay() {
        this.volumeRange.value = this.video.muted ? 0 : this.video.volume * 100;
        // Update volume icon based on level
    }
    
    // Utility methods
    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
    
    isVideoFocused() {
        return this.container.contains(document.activeElement) || 
               document.activeElement === document.body;
    }
    
    showControls() {
        this.controls.style.opacity = '1';
        clearTimeout(this.hideControlsTimeout);
        this.hideControlsTimeout = setTimeout(() => this.hideControls(), 3000);
    }
    
    hideControls() {
        if (!this.video.paused) {
            this.controls.style.opacity = '0';
        }
    }
    
    // Advanced features (to be implemented)
    toggleFullscreen() {
        // Fullscreen implementation
    }
    
    togglePiP() {
        // Picture-in-Picture implementation
    }
    
    setPlaybackRate(event) {
        // Playback speed implementation
    }
    
    setQuality(event) {
        // Quality switching implementation
    }
    
    toggleSpeedMenu() {
        // Speed menu toggle
    }
    
    toggleQualityMenu() {
        // Quality menu toggle
    }
}

// Export for use
window.EnhancedVideoPlayer = EnhancedVideoPlayer;
