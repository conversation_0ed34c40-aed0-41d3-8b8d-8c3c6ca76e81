<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Validation Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .validation-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .status.success {
            background-color: #4caf50;
            color: white;
        }
        .status.error {
            background-color: #f44336;
            color: white;
        }
        .status.warning {
            background-color: #ff9800;
            color: white;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #4caf50;
            transition: width 0.3s ease;
        }
        button {
            background-color: #4ecdc4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45b7aa;
        }
        .file-list {
            max-height: 300px;
            overflow-y: auto;
            background: #333;
            padding: 10px;
            border-radius: 4px;
        }
        .file-item {
            padding: 5px;
            border-bottom: 1px solid #444;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        .file-status.ok {
            background-color: #4caf50;
        }
        .file-status.missing {
            background-color: #f44336;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Media Validation Tool</h1>
        <p>This tool checks if all media files referenced in your website are accessible.</p>
    </div>

    <div class="validation-section">
        <h2>Validation Status</h2>
        <div id="overallStatus" class="status">Ready to validate</div>
        <div class="progress">
            <div id="progressBar" class="progress-bar" style="width: 0%"></div>
        </div>
        <button onclick="startValidation()">Start Validation</button>
        <button onclick="downloadReport()">Download Report</button>
    </div>

    <div class="validation-section">
        <h2>Results Summary</h2>
        <div id="summary">
            <p>Total Files: <span id="totalFiles">0</span></p>
            <p>Accessible: <span id="accessibleFiles">0</span></p>
            <p>Missing: <span id="missingFiles">0</span></p>
        </div>
    </div>

    <div class="validation-section">
        <h2>Detailed Results</h2>
        <div id="detailedResults" class="file-list">
            <p>Click "Start Validation" to begin checking files...</p>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        let validationResults = [];
        let totalFiles = 0;
        let checkedFiles = 0;

        async function startValidation() {
            // Wait for app to load
            if (!window.app || !window.app.videos) {
                document.getElementById('overallStatus').innerHTML = 'Loading application data...';
                setTimeout(startValidation, 1000);
                return;
            }

            document.getElementById('overallStatus').innerHTML = 'Starting validation...';
            document.getElementById('overallStatus').className = 'status warning';
            
            validationResults = [];
            checkedFiles = 0;
            
            // Collect all media URLs
            const mediaUrls = new Set();
            
            // Add video thumbnails and videos
            window.app.videos.forEach(video => {
                if (video.thumbnail && !video.thumbnail.includes('file.svg')) {
                    mediaUrls.add(video.thumbnail);
                }
                if (video.videoUrl) {
                    mediaUrls.add(video.videoUrl);
                }
            });

            // Add photos
            if (window.app.newPhotos) {
                window.app.newPhotos.forEach(photo => {
                    if (photo.videoUrl) {
                        mediaUrls.add(photo.videoUrl);
                    }
                    if (photo.thumbnail) {
                        mediaUrls.add(photo.thumbnail);
                    }
                });
            }

            totalFiles = mediaUrls.size;
            document.getElementById('totalFiles').textContent = totalFiles;
            
            // Check each file
            for (const url of mediaUrls) {
                await checkFile(url);
            }
            
            // Update final status
            const missingCount = validationResults.filter(r => !r.accessible).length;
            const accessibleCount = validationResults.filter(r => r.accessible).length;
            
            document.getElementById('accessibleFiles').textContent = accessibleCount;
            document.getElementById('missingFiles').textContent = missingCount;
            
            if (missingCount === 0) {
                document.getElementById('overallStatus').innerHTML = 'All files accessible!';
                document.getElementById('overallStatus').className = 'status success';
            } else {
                document.getElementById('overallStatus').innerHTML = `${missingCount} files missing or inaccessible`;
                document.getElementById('overallStatus').className = 'status error';
            }
        }

        async function checkFile(url) {
            try {
                const resolvedUrl = window.app.resolvePath(url);
                const response = await fetch(resolvedUrl, { method: 'HEAD' });
                const accessible = response.ok;
                
                validationResults.push({
                    url: url,
                    resolvedUrl: resolvedUrl,
                    accessible: accessible,
                    status: response.status
                });
                
                updateProgress();
                updateDetailedResults();
                
            } catch (error) {
                validationResults.push({
                    url: url,
                    resolvedUrl: window.app.resolvePath(url),
                    accessible: false,
                    error: error.message
                });
                
                updateProgress();
                updateDetailedResults();
            }
        }

        function updateProgress() {
            checkedFiles++;
            const progress = (checkedFiles / totalFiles) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        function updateDetailedResults() {
            const container = document.getElementById('detailedResults');
            container.innerHTML = '';
            
            validationResults.forEach(result => {
                const item = document.createElement('div');
                item.className = 'file-item';
                
                const fileName = result.url.split('/').pop();
                const status = result.accessible ? 'ok' : 'missing';
                const statusText = result.accessible ? 'OK' : 'MISSING';
                
                item.innerHTML = `
                    <span title="${result.resolvedUrl}">${fileName}</span>
                    <span class="file-status ${status}">${statusText}</span>
                `;
                
                container.appendChild(item);
            });
        }

        function downloadReport() {
            if (validationResults.length === 0) {
                alert('Please run validation first');
                return;
            }
            
            const report = validationResults.map(result => ({
                file: result.url,
                resolvedUrl: result.resolvedUrl,
                accessible: result.accessible,
                status: result.status || 'error',
                error: result.error || ''
            }));
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'media-validation-report.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Auto-start validation when page loads
        window.addEventListener('load', () => {
            setTimeout(startValidation, 2000);
        });
    </script>
</body>
</html>
