<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2a2a2a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="300" height="200" fill="url(#bg)"/>
  <circle cx="120" cy="90" r="25" fill="#4ecdc4" opacity="0.8"/>
  <circle cx="180" cy="90" r="25" fill="#4ecdc4" opacity="0.8"/>
  <polygon points="110,85 110,95 125,90" fill="white"/>
  <polygon points="170,85 170,95 185,90" fill="white"/>
  <path d="M 140 90 Q 150 80 160 90" stroke="white" stroke-width="2" fill="none"/>
  <text x="150" y="140" text-anchor="middle" fill="#b3b3b3" font-family="Arial, sans-serif" font-size="12">Lesbian</text>
</svg>
