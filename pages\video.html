<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Player - PornTubeX</title>
    <meta name="description" content="Watch premium adult content on PornTubeX">
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="../index.html" class="brand-link">
                        <h1 class="brand-title">PornTubeX</h1>
                    </a>
                </div>
                
                <div class="nav-search">
                    <form class="search-form" id="searchForm">
                        <input type="text" class="search-input" placeholder="Search videos..." id="searchInput">
                        <button type="submit" class="search-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                        </button>
                    </form>
                </div>
                
                <div class="nav-menu">
                    <a href="../index.html" class="nav-link">Home</a>
                    <a href="#" class="nav-link">Browse</a>
                    <a href="#" class="nav-link">Categories</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content video-page">
        <div class="container">
            <div class="video-layout">
                <!-- Video Player Section -->
                <div class="video-player-section">
                    <div class="video-player-container">
                        <video class="video-player" id="videoPlayer" controls poster="">
                            <source src="" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    
                    <!-- Video Info -->
                    <div class="video-info">
                        <h1 class="video-title" id="videoTitle">Loading...</h1>
                        
                        <div class="video-meta">
                            <div class="video-stats">
                                <span class="views" id="videoViews">0 views</span>
                                <span class="upload-date" id="uploadDate">Loading...</span>
                                <span class="duration" id="videoDuration">0:00</span>
                            </div>
                            
                            <div class="video-actions">
                                <button class="action-btn like-btn" id="likeBtn">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                                    </svg>
                                    <span id="likeCount">0</span>
                                </button>
                                
                                <button class="action-btn favorite-btn" id="favoriteBtn">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                                    </svg>
                                    Favorite
                                </button>
                                
                                <button class="action-btn share-btn" id="shareBtn">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="18" cy="5" r="3"></circle>
                                        <circle cx="6" cy="12" r="3"></circle>
                                        <circle cx="18" cy="19" r="3"></circle>
                                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                                    </svg>
                                    Share
                                </button>
                            </div>
                        </div>
                        
                        <!-- Video Description -->
                        <div class="video-description">
                            <p id="videoDescription">Loading description...</p>
                        </div>
                        
                        <!-- Video Tags -->
                        <div class="video-tags" id="videoTags">
                            <!-- Tags will be loaded dynamically -->
                        </div>
                        
                        <!-- Uploader Info -->
                        <div class="uploader-info" id="uploaderInfo">
                            <!-- Uploader info will be loaded dynamically -->
                        </div>
                    </div>
                    
                    <!-- Comments Section -->
                    <div class="comments-section">
                        <h3 class="comments-title">Comments</h3>
                        
                        <!-- Comment Form -->
                        <form class="comment-form" id="commentForm">
                            <div class="comment-input-container">
                                <textarea class="comment-input" id="commentInput" placeholder="Add a comment..." rows="3"></textarea>
                                <button type="submit" class="comment-submit-btn">Post Comment</button>
                            </div>
                        </form>
                        
                        <!-- Comments List -->
                        <div class="comments-list" id="commentsList">
                            <!-- Comments will be loaded dynamically -->
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <aside class="video-sidebar">
                    <!-- Related Videos -->
                    <div class="sidebar-section">
                        <h3 class="sidebar-title">Related Videos</h3>
                        <div class="related-videos" id="relatedVideos">
                            <!-- Related videos will be loaded dynamically -->
                        </div>
                    </div>
                    
                    <!-- Other Videos -->
                    <div class="sidebar-section">
                        <h3 class="sidebar-title">More Videos</h3>
                        <div class="other-videos" id="otherVideos">
                            <!-- Other videos will be loaded dynamically -->
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </main>

    <!-- Share Modal -->
    <div class="modal" id="shareModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Share Video</h3>
                <button class="modal-close" id="closeShareModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="share-options">
                    <button class="share-option" id="copyLinkBtn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                        </svg>
                        Copy Link
                    </button>
                </div>
                <div class="share-link-container">
                    <input type="text" class="share-link-input" id="shareLinkInput" readonly>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/video-player.js"></script>
</body>
</html>
