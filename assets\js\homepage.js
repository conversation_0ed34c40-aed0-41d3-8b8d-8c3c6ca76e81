// Homepage specific functionality
class Homepage {
    constructor(app) {
        this.app = app;
        this.init();
    }
    
    async init() {
        // Wait for app to be ready
        if (!this.app.videos || !this.app.videos.length) {
            console.log('Homepage.init: Waiting for videos to load...');
            setTimeout(() => this.init(), 100);
            return;
        }

        console.log('Homepage.init: Starting to load content with', this.app.videos.length, 'videos');

        try {
            this.loadFeaturedVideos();
            this.loadPhotos();
            this.loadRecentVideos();
            this.setupFilterListeners();
            console.log('Homepage.init: All content loaded successfully');
        } catch (error) {
            console.error('Homepage.init: Error loading content:', error);
        }
    }
    

    
    loadFeaturedVideos() {
        const featuredVideos = document.getElementById('featuredVideos');
        if (!featuredVideos) {
            console.log('Homepage.loadFeaturedVideos: featuredVideos element not found');
            return;
        }

        featuredVideos.innerHTML = '';

        // Get top-rated videos only (no photos)
        const featured = this.app.videos
            .filter(video => video.type !== 'photo')
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 6);

        console.log('Homepage.loadFeaturedVideos: Loading', featured.length, 'featured videos');

        featured.forEach(video => {
            try {
                const videoCard = this.app.createVideoCard(video);
                featuredVideos.appendChild(videoCard);
            } catch (error) {
                console.error('Homepage.loadFeaturedVideos: Error creating video card:', error, video);
            }
        });

        console.log('Homepage.loadFeaturedVideos: Added', featuredVideos.children.length, 'video cards');
    }
    
    loadPhotos() {
        const photosGrid = document.getElementById('photosGrid');
        if (!photosGrid) {
            console.log('Homepage.loadPhotos: photosGrid element not found');
            return;
        }

        photosGrid.innerHTML = '';

        // Get all photos sorted by rating
        const photos = (this.app.newPhotos || [])
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 12);

        console.log('Homepage.loadPhotos: Loading', photos.length, 'photos');

        photos.forEach(photo => {
            try {
                const photoCard = this.app.createVideoCard(photo);
                photosGrid.appendChild(photoCard);
            } catch (error) {
                console.error('Homepage.loadPhotos: Error creating photo card:', error, photo);
            }
        });

        console.log('Homepage.loadPhotos: Added', photosGrid.children.length, 'photo cards');
    }

    loadRecentVideos() {
        const recentVideos = document.getElementById('recentVideos');
        if (!recentVideos) return;

        recentVideos.innerHTML = '';

        // Get most recent videos only (no photos)
        const recent = this.app.videos
            .filter(video => video.type !== 'photo')
            .sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate))
            .slice(0, 8);

        recent.forEach(video => {
            const videoCard = this.app.createVideoCard(video);
            recentVideos.appendChild(videoCard);
        });
    }

    setupFilterListeners() {
        const applyFiltersBtn = document.getElementById('applyFilters');
        const clearFiltersBtn = document.getElementById('clearFilters');

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => this.applyFilters());
        }

        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => this.clearFilters());
        }
    }

    applyFilters() {
        const categoryFilter = document.getElementById('categoryFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const sortFilter = document.getElementById('sortFilter').value;
        const ratingFilter = parseFloat(document.getElementById('ratingFilter').value);

        const criteria = {
            category: categoryFilter,
            type: typeFilter,
            sortBy: sortFilter,
            minRating: ratingFilter,
            sortOrder: 'desc'
        };

        const filteredResults = this.app.advancedFilter(criteria);
        this.displayFilteredResults(filteredResults);
    }

    clearFilters() {
        // Reset all filter controls
        document.getElementById('categoryFilter').value = 'all';
        document.getElementById('typeFilter').value = 'all';
        document.getElementById('sortFilter').value = 'rating';
        document.getElementById('ratingFilter').value = '0';

        // Reload original content
        this.loadFeaturedVideos();
        this.loadPhotos();
        this.loadRecentVideos();
    }

    displayFilteredResults(results) {
        // Separate videos and photos from results
        const videos = results.filter(item => item.type !== 'photo');
        const photos = results.filter(item => item.type === 'photo');

        // Update featured videos section
        const featuredVideos = document.getElementById('featuredVideos');
        if (featuredVideos) {
            featuredVideos.innerHTML = '';

            if (videos.length === 0) {
                featuredVideos.innerHTML = '<p class="no-results">No videos match your filters.</p>';
            } else {
                // Show first 6 video results in featured section
                videos.slice(0, 6).forEach(video => {
                    const videoCard = this.app.createVideoCard(video);
                    featuredVideos.appendChild(videoCard);
                });
            }
        }

        // Update photos section
        const photosGrid = document.getElementById('photosGrid');
        if (photosGrid) {
            photosGrid.innerHTML = '';

            if (photos.length === 0) {
                photosGrid.innerHTML = '<p class="no-results">No photos match your filters.</p>';
            } else {
                // Show first 12 photo results
                photos.slice(0, 12).forEach(photo => {
                    const photoCard = this.app.createVideoCard(photo);
                    photosGrid.appendChild(photoCard);
                });
            }
        }

        // Update recent videos section
        const recentVideos = document.getElementById('recentVideos');
        if (recentVideos) {
            recentVideos.innerHTML = '';

            if (videos.length <= 6) {
                recentVideos.innerHTML = '<p class="no-results">No additional videos to show.</p>';
            } else {
                // Show next 8 video results in recent section
                videos.slice(6, 14).forEach(video => {
                    const videoCard = this.app.createVideoCard(video);
                    recentVideos.appendChild(videoCard);
                });
            }
        }
    }
}

// Initialize homepage when app is ready
document.addEventListener('DOMContentLoaded', () => {
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max wait time

    const initHomepage = () => {
        attempts++;

        // Check if app exists and has been initialized
        if (window.app && window.app.videos && window.app.videos.length > 0) {
            console.log('Homepage: App found with', window.app.videos.length, 'videos');
            new Homepage(window.app);
        } else if (attempts < maxAttempts) {
            // Log what we're waiting for
            if (!window.app) {
                console.log('Homepage: Waiting for app to be created...');
            } else if (!window.app.videos) {
                console.log('Homepage: Waiting for videos array to be created...');
            } else {
                console.log('Homepage: Waiting for videos to be loaded... (current count:', window.app.videos.length, ')');
            }
            setTimeout(initHomepage, 100);
        } else {
            console.error('Homepage: Failed to initialize after', maxAttempts, 'attempts');
            console.log('Homepage: window.app =', window.app);
            if (window.app) {
                console.log('Homepage: window.app.videos =', window.app.videos);
            }
        }
    };

    initHomepage();
});
