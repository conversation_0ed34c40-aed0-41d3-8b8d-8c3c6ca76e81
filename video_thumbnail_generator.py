#!/usr/bin/env python3
"""
Video Thumbnail Generator

This script extracts random frames from video files and saves them as image thumbnails.
Supports multiple video formats and includes comprehensive error handling.

Requirements:
- opencv-python (cv2)
- Pillow (PIL)

Usage:
    python video_thumbnail_generator.py [input_folder] [output_folder]
"""

import os
import sys
import random
import logging
from pathlib import Path
from typing import List, Tuple, Optional
import cv2
from PIL import Image, ImageEnhance
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('thumbnail_generator.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class VideoThumbnailGenerator:
    """Generates thumbnails from video files by extracting random frames."""
    
    # Supported video formats
    SUPPORTED_FORMATS = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.ts', '.m4v'}
    
    # Output image format
    OUTPUT_FORMAT = '.jpg'
    OUTPUT_QUALITY = 85  # JPEG quality (1-100)
    
    def __init__(self, input_folder: str, output_folder: str = "Thumbnails"):
        """
        Initialize the thumbnail generator.
        
        Args:
            input_folder: Path to folder containing video files
            output_folder: Path to folder where thumbnails will be saved
        """
        self.input_folder = Path(input_folder)
        self.output_folder = Path(output_folder)
        
        # Create output folder if it doesn't exist
        self.output_folder.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Input folder: {self.input_folder}")
        logger.info(f"Output folder: {self.output_folder}")
    
    def get_video_files(self) -> List[Path]:
        """
        Get all supported video files from the input folder.
        
        Returns:
            List of video file paths
        """
        video_files = []
        
        if not self.input_folder.exists():
            logger.error(f"Input folder does not exist: {self.input_folder}")
            return video_files
        
        for file_path in self.input_folder.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.SUPPORTED_FORMATS:
                video_files.append(file_path)
        
        logger.info(f"Found {len(video_files)} video files")
        return video_files
    
    def get_video_info(self, video_path: Path) -> Optional[Tuple[int, float]]:
        """
        Get video information including frame count and FPS.
        
        Args:
            video_path: Path to video file
            
        Returns:
            Tuple of (frame_count, fps) or None if error
        """
        try:
            cap = cv2.VideoCapture(str(video_path))
            
            if not cap.isOpened():
                logger.error(f"Cannot open video: {video_path}")
                return None
            
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            cap.release()
            
            if frame_count <= 0 or fps <= 0:
                logger.error(f"Invalid video properties: {video_path}")
                return None
            
            return frame_count, fps
            
        except Exception as e:
            logger.error(f"Error getting video info for {video_path}: {e}")
            return None
    
    def extract_random_frame(self, video_path: Path) -> Optional[Image.Image]:
        """
        Extract a random frame from the video.
        
        Args:
            video_path: Path to video file
            
        Returns:
            PIL Image object or None if error
        """
        try:
            # Get video information
            video_info = self.get_video_info(video_path)
            if not video_info:
                return None
            
            frame_count, fps = video_info
            
            # Choose a random frame (avoid first and last 5% of video)
            start_frame = int(frame_count * 0.05)
            end_frame = int(frame_count * 0.95)
            
            if start_frame >= end_frame:
                # For very short videos, use any frame except first and last
                start_frame = 1
                end_frame = max(2, frame_count - 1)
            
            random_frame = random.randint(start_frame, end_frame)
            
            logger.info(f"Extracting frame {random_frame}/{frame_count} from {video_path.name}")
            
            # Open video and seek to random frame
            cap = cv2.VideoCapture(str(video_path))
            cap.set(cv2.CAP_PROP_POS_FRAMES, random_frame)
            
            ret, frame = cap.read()
            cap.release()
            
            if not ret or frame is None:
                logger.error(f"Failed to read frame {random_frame} from {video_path}")
                return None
            
            # Convert BGR to RGB (OpenCV uses BGR, PIL uses RGB)
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Convert to PIL Image
            pil_image = Image.fromarray(frame_rgb)
            
            return pil_image
            
        except Exception as e:
            logger.error(f"Error extracting frame from {video_path}: {e}")
            return None
    
    def enhance_image(self, image: Image.Image) -> Image.Image:
        """
        Apply basic image enhancements to improve thumbnail quality.
        
        Args:
            image: PIL Image object
            
        Returns:
            Enhanced PIL Image object
        """
        try:
            # Slightly enhance contrast and sharpness
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
            
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            return image
            
        except Exception as e:
            logger.warning(f"Error enhancing image: {e}")
            return image
    
    def generate_thumbnail_name(self, video_path: Path) -> str:
        """
        Generate thumbnail filename based on video filename.
        
        Args:
            video_path: Path to video file
            
        Returns:
            Thumbnail filename
        """
        base_name = video_path.stem
        return f"{base_name}{self.OUTPUT_FORMAT}"
    
    def process_video(self, video_path: Path) -> bool:
        """
        Process a single video file and generate thumbnail.
        
        Args:
            video_path: Path to video file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Processing: {video_path.name}")
            
            # Generate thumbnail filename
            thumbnail_name = self.generate_thumbnail_name(video_path)
            thumbnail_path = self.output_folder / thumbnail_name
            
            # Skip if thumbnail already exists
            if thumbnail_path.exists():
                logger.info(f"Thumbnail already exists: {thumbnail_name}")
                return True
            
            # Extract random frame
            frame_image = self.extract_random_frame(video_path)
            if not frame_image:
                return False
            
            # Enhance image quality
            frame_image = self.enhance_image(frame_image)
            
            # Save thumbnail
            frame_image.save(
                thumbnail_path,
                format='JPEG',
                quality=self.OUTPUT_QUALITY,
                optimize=True
            )
            
            logger.info(f"Thumbnail saved: {thumbnail_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing {video_path}: {e}")
            return False
    
    def process_all_videos(self) -> Tuple[int, int]:
        """
        Process all video files in the input folder.
        
        Returns:
            Tuple of (successful_count, total_count)
        """
        video_files = self.get_video_files()
        
        if not video_files:
            logger.warning("No video files found to process")
            return 0, 0
        
        successful_count = 0
        
        for video_path in video_files:
            if self.process_video(video_path):
                successful_count += 1
        
        logger.info(f"Processing complete: {successful_count}/{len(video_files)} successful")
        return successful_count, len(video_files)


def main():
    """Main function to run the thumbnail generator."""
    parser = argparse.ArgumentParser(description='Generate thumbnails from video files')
    parser.add_argument(
        'input_folder',
        nargs='?',
        default='categories/Videos',
        help='Input folder containing video files (default: categories/Videos)'
    )
    parser.add_argument(
        'output_folder',
        nargs='?',
        default='categories/Thumbnails',
        help='Output folder for thumbnails (default: categories/Thumbnails)'
    )
    parser.add_argument(
        '--overwrite',
        action='store_true',
        help='Overwrite existing thumbnails'
    )
    
    args = parser.parse_args()
    
    try:
        # Create thumbnail generator
        generator = VideoThumbnailGenerator(args.input_folder, args.output_folder)
        
        # Process all videos
        successful, total = generator.process_all_videos()
        
        print(f"\n{'='*50}")
        print(f"THUMBNAIL GENERATION COMPLETE")
        print(f"{'='*50}")
        print(f"Total videos processed: {total}")
        print(f"Successful thumbnails: {successful}")
        print(f"Failed: {total - successful}")
        print(f"Output folder: {args.output_folder}")
        
        if successful > 0:
            print(f"\n✅ Successfully generated {successful} thumbnails!")
        
        if total - successful > 0:
            print(f"\n⚠️  {total - successful} videos failed to process. Check the log for details.")
        
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
