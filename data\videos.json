{"videos": [{"id": "v001", "title": "Passionate Romance", "description": "A beautiful romantic encounter between two lovers", "thumbnail": "https://via.placeholder.com/300x200/667eea/ffffff?text=Romantic+Video", "videoUrl": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4", "duration": "12:34", "uploadDate": "2024-06-15", "uploader": {"id": "u001", "username": "RomanticLover", "avatar": "https://via.placeholder.com/50x50/f093fb/ffffff?text=RL"}, "tags": ["romantic", "passionate", "intimate", "couple"], "category": "romantic", "subcategory": "couples", "views": 15420, "likes": 892, "rating": 4.8}, {"id": "v002", "title": "Sensual Massage", "description": "Relaxing and sensual massage techniques", "thumbnail": "https://via.placeholder.com/300x200/4facfe/ffffff?text=Massage+Video", "videoUrl": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4", "duration": "18:45", "uploadDate": "2024-06-14", "uploader": {"id": "u002", "username": "MassageExpert", "avatar": "https://via.placeholder.com/50x50/43e97b/ffffff?text=ME"}, "tags": ["massage", "sensual", "relaxing", "therapeutic"], "category": "massage", "subcategory": "sensual", "views": 23150, "likes": 1205, "rating": 4.9}, {"id": "v003", "title": "Artistic Expression", "description": "Beautiful artistic expression of human form", "thumbnail": "https://via.placeholder.com/300x200/fa709a/ffffff?text=Artistic+Video", "videoUrl": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4", "duration": "8:22", "uploadDate": "2024-06-13", "uploader": {"id": "u003", "username": "ArtisticSoul", "avatar": "https://via.placeholder.com/50x50/a8edea/ffffff?text=AS"}, "tags": ["artistic", "beautiful", "expression", "aesthetic"], "category": "artistic", "subcategory": "photography", "views": 9876, "likes": 654, "rating": 4.7}, {"id": "v004", "title": "Intimate Connection", "description": "Deep emotional and physical connection", "thumbnail": "https://via.placeholder.com/300x200/ffecd2/333333?text=Intimate+Video", "videoUrl": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4", "duration": "15:30", "uploadDate": "2024-06-12", "uploader": {"id": "u001", "username": "RomanticLover", "avatar": "assets/images/avatar1.jpg"}, "tags": ["intimate", "connection", "emotional", "passionate"], "category": "romantic", "subcategory": "intimate", "views": 18750, "likes": 1123, "rating": 4.8}, {"id": "v005", "title": "Wellness Journey", "description": "Exploring wellness and self-care practices", "thumbnail": "https://via.placeholder.com/300x200/ff8a80/ffffff?text=Wellness+Video", "videoUrl": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4", "duration": "22:15", "uploadDate": "2024-06-11", "uploader": {"id": "u004", "username": "WellnessGuru", "avatar": "https://via.placeholder.com/50x50/667eea/ffffff?text=WG"}, "tags": ["wellness", "self-care", "meditation", "mindfulness"], "category": "wellness", "subcategory": "meditation", "views": 12340, "likes": 789, "rating": 4.6}, {"id": "v006", "title": "Dance of Desire", "description": "Expressive dance showcasing human movement", "thumbnail": "https://via.placeholder.com/300x200/764ba2/ffffff?text=Dance+Video", "videoUrl": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4", "duration": "10:45", "uploadDate": "2024-06-10", "uploader": {"id": "u005", "username": "DanceArtist", "avatar": "https://via.placeholder.com/50x50/fcb69f/ffffff?text=DA"}, "tags": ["dance", "movement", "expressive", "artistic"], "category": "artistic", "subcategory": "dance", "views": 16890, "likes": 945, "rating": 4.7}]}