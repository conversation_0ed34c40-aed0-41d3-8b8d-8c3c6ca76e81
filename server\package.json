{"name": "thumbnail-server", "version": "1.0.0", "description": "Server for handling thumbnail uploads and management", "main": "thumbnail-server.js", "scripts": {"start": "node thumbnail-server.js", "dev": "nodemon thumbnail-server.js"}, "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["thumbnail", "upload", "express", "server"], "author": "Thumbnail System", "license": "MIT"}