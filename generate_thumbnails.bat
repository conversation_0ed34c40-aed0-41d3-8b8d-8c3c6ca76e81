@echo off
echo ========================================
echo Video Thumbnail Generator
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Installing required packages...
pip install -r requirements.txt

echo.
echo Generating thumbnails...
python video_thumbnail_generator.py

echo.
echo Press any key to exit...
pause >nul
